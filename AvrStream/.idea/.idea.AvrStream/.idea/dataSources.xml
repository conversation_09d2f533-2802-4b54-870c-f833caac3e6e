<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="GatewayUser_dev@localhost" uuid="38470ff5-9588-4bc9-b086-9e1382fb65c9">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <configured-by-url>true</configured-by-url>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>**************************************************************************************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="Gateway_dev@***************" uuid="99cbd8fe-34ff-4430-9df3-a42475e0e8f7">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <configured-by-url>true</configured-by-url>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>jdbc:postgresql://***************:5432/Gateway_dev?password=yourStrong%28%21%21%29Pa%24%24word&amp;user=postgres</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.host.port" />
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
        <property name="com.intellij.clouds.kubernetes.db.container.port" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="Gateway_dev@localhost" uuid="1701b2cb-4183-45b3-8792-c03a87c9ffac">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <configured-by-url>true</configured-by-url>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>**********************************************************************************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="Gateway_dev@*************" uuid="757d9c5a-de73-44d6-8047-d7c03a2b537a">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <configured-by-url>true</configured-by-url>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>jdbc:postgresql://*************:5432/Gateway_dev?password=yourStrong%28%21%21%29Pa%24%24word&amp;user=postgres</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="VmsUser_dev@**************" uuid="22f16c91-3ea6-4c15-8217-002fed143c20">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <configured-by-url>true</configured-by-url>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>jdbc:postgresql://**************:5432/VmsUser_dev?password=yourStrong%28%21%21%29Pa%24%24word&amp;user=postgres</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="Vms_dev@************** [2]" uuid="e03ef34a-997d-4630-89a3-9d9dd3fc9b73">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <configured-by-url>true</configured-by-url>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>jdbc:postgresql://**************:5432/Vms_dev?password=yourStrong%28%21%21%29Pa%24%24word&amp;user=postgres</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>