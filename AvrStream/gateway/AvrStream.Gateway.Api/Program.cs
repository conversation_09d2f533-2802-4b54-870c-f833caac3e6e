using System.Text;
using AvrStream.Base.Jwt;
using AvrStream.Gateway.Api.Hub;
using AvrStream.Gateway.Api.SeedData;
using AvrStream.Gateway.Api.Services;
using AvrStream.Gateway.Api.Workers;
using AvrStream.Gateway.Entities;
using AvrStream.Gateway.Entities.Databases;
using AvrStream.Gateway.Entities.Models;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Serilog;

var builder = WebApplication.CreateBuilder(args);
var services = builder.Services;
var logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .CreateLogger();
builder.Logging.AddSerilog(logger);
services.AddControllers();
services.AddUserEntities(builder.Configuration.GetConnectionString("GatewayUser"));
services.AddGatewayEntities(builder.Configuration.GetConnectionString("Gateway"));
services.AddIdentity<AppUser, IdentityRole>((options =>
    {
        options.Password.RequireDigit = false;
        options.Password.RequiredLength = 5;
        options.Password.RequireLowercase = false;
        options.Password.RequireUppercase = false;
        options.Password.RequireNonAlphanumeric = false;
        options.SignIn.RequireConfirmedAccount = false;
    }))
    .AddEntityFrameworkStores<UserDbContext>()
    .AddDefaultTokenProviders();
services.AddCors(options =>
{
    options.AddDefaultPolicy(
        corsPolicyBuilder =>
        {
            corsPolicyBuilder
                .AllowAnyMethod()
                .AllowAnyHeader()
                .SetIsOriginAllowed(_ => true)
                .AllowCredentials();
        });
});


//Scoped Service
services.AddScoped<Jwt>();
services.AddScoped<Token>();
services.AddScoped<AccountService>();
services.AddScoped<RoleService>();


//Singleton Service
services.AddSingleton<RecordService>();
services.AddSingleton<ClientService>();
services.AddSingleton<MessageService>();
services.AddSingleton<DeviceService>();
services.AddSingleton<ResourceService>();
services.AddSingleton<SystemConfigurationService>();
services.AddSingleton<CameraSettingService>();
services.AddSingleton<RecordingFileService>();


//Hosted Service
services.AddHostedService<StartupHostedService>();
services.AddHostedService<SystemInfoWorker>();
services.AddHostedService<RecordingFileSyncerWorker>();
services.AddHostedService<EncryptWorker>();
services.AddHostedService<CleanupWorker>();

services.AddSignalR();
services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
}).AddJwtBearer(JwtBearerDefaults.AuthenticationScheme, options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        ValidateIssuer = false,
        ValidateLifetime = true,
        IssuerSigningKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"] ?? throw new InvalidOperationException())),
        ValidateAudience = false,
        ClockSkew = TimeSpan.Zero
    };
});
services.AddAuthorization(options =>
{
    //Account
    options.AddPolicy("CreateAccount", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("CreateAccount", new string[] { "True" });
    });
    options.AddPolicy("UpdateAccount", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("UpdateAccount", new string[] { "True" });
    });
    options.AddPolicy("DeleteAccount", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("DeleteAccount", new string[] { "True" });
    });
    options.AddPolicy("GetAccount", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("GetAccount", new string[] { "True" });
    });

    options.AddPolicy("ResetPassword", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("ResetPassword", new string[] { "True" });
    });

    options.AddPolicy("UpdatePermission", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("UpdatePermission", new string[] { "True" });
    });

    //Role

    options.AddPolicy("CreateRole", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("CreateRole", new string[] { "True" });
    });
    options.AddPolicy("UpdateRole", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("UpdateRole", new string[] { "True" });
    });
    options.AddPolicy("DeleteRole", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("DeleteRole", new string[] { "True" });
    });
    options.AddPolicy("GetRole", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("GetRole", new string[] { "True" });
    });

    // Device

    options.AddPolicy("GetDevice", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("GetDevice", new string[] { "True" });
    });

    options.AddPolicy("UpdateDevice", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("UpdateDevice", new string[] { "True" });
    });

    // Recording File
    options.AddPolicy("GetRecordingFile", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("GetRecordingFile", new string[] { "True" });
    });

    options.AddPolicy("DeleteRecordingFile", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("DeleteRecordingFile", new string[] { "True" });
    });

    // Preview

    options.AddPolicy("Preview", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("Preview", new string[] { "True" });
    });

    // Setting

    options.AddPolicy("CameraSetting", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("CameraSetting", new string[] { "True" });
    });

    options.AddPolicy("SystemSetting", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("SystemSetting", new string[] { "True" });
    });
});
var app = builder.Build();
app.UseCors();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();
app.MapHub<DashboardHub>("/DashboardHub");
app.Lifetime.ApplicationStarted.Register(() =>
{
    var factory = app.Services.GetRequiredService<IServiceScopeFactory>();
    using var scope = factory.CreateScope();
    var userDb = scope.ServiceProvider.GetRequiredService<UserDbContext>();
    if (userDb.Database.IsRelational())
    {
        userDb.Database.Migrate();
        var userManager = scope.ServiceProvider.GetRequiredService<UserManager<AppUser>>();
        var userLogger = scope.ServiceProvider.GetRequiredService<ILogger<UserDbContextSeed>>();
        var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole>>();
        UserDbContextSeed.SeedAsync(userDb, roleManager, userManager, userLogger).Wait();
    }
});
app.Run();