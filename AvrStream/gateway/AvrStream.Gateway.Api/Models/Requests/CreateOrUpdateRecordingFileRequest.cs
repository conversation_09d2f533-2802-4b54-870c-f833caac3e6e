namespace AvrStream.Gateway.Api.Models.Requests;

public class CreateOrUpdateRecordingFileRequest
{
    public Guid? Id { get; set; }
    public string Name { get; set; }
    public string Password { get; set; }
    public string Iv { get; set; }
    public bool IsEncrypt { get; set; }
    public bool EncryptDone { get; set; }
    public string Path { get; set; }
    public bool IsEndRecordingFile { get; set; }
    public bool IsSynchronized { get; set; }
    public DateTime? SynchronizedDate { get; set; }
}