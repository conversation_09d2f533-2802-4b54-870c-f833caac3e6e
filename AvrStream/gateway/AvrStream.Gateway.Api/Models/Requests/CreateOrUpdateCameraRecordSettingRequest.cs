using AvrStream.Gateway.Entities.Enums;

namespace AvrStream.Gateway.Api.Models.Requests;

public class CreateOrUpdateCameraRecordSettingRequest
{
    public Guid? Id { get; set; }
    public string Name { get; set; }
    public string Bus { get; set; }
    public int FocusValue { get; set; }
    public bool IsAutoFocus { get; set; }
    public CameraResolution Resolution { get; set; }
    public int Fps { get; set; }
    public bool IsActive { get; set; }
    public int BitRate { get; set; }
}