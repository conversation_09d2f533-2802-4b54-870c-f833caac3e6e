using AvrStream.Gateway.Api.Dto;

namespace AvrStream.Gateway.Api.Models;

public class DashboardInfo
{
    public double Cpu { get; set; }
    public double Memory { get; set; }
    public string DiskUsed { get; set; }
    public string DiskTotal { get; set; }
    public int CpuTemp { get; set; }
    public DeviceStatus StatusCam1 { get; set; }
    public DeviceStatus StatusCam2 { get; set; }
    public DeviceStatus StatusMic1 { get; set; }
    public DeviceStatus StatusMic2 { get; set; }
    public List<MessageDto> Messages { get; set; }
}