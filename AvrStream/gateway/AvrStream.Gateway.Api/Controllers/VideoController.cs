using AvrStream.Gateway.Entities.Databases;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Gateway.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class VideoController : ControllerBase
    {
        private readonly GatewayDbContext _db;
        private readonly ILogger<VideoController> _logger;

        public VideoController(GatewayDbContext db, IConfiguration configuration, ILogger<VideoController> logger)
        {
            _db = db;
            _logger = logger;
        }

        [HttpGet]
        [Route("{id}")]
        public async Task<IActionResult> GetVideo([FromRoute] Guid id)
        {
            var file = await _db.RecordingFiles.SingleOrDefaultAsync(x => x.Id == id);
            if (file == null)
            {
                return NoContent();
            }
            if (!System.IO.File.Exists(file.Path))
            {
                return NoContent();
            }

            var fileStream = new FileStream(file.Name, FileMode.Open, FileAccess.Read, FileShare.Read);
            return File(fileStream, "video/mp4", enableRangeProcessing: true);
        }
    }
}
