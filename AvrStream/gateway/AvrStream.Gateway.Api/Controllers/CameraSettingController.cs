using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AvrStream.Gateway.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CameraSettingController : ControllerBase
    {
        private readonly CameraSettingService _cameraSettingService;
        private readonly RecordService _recordService;

        public CameraSettingController(CameraSettingService cameraSettingService, RecordService recordService)
        {
            _cameraSettingService = cameraSettingService;
            _recordService = recordService;
        }

        [HttpGet]
        [Authorize(Policy = "CameraSetting")]
        public async Task<IActionResult> ListCameraSettingsAsync([FromQuery] int page = 1, [FromQuery] int take = 10,
            [FromQuery] bool isTakeAll = false)
        {
            var result = await _cameraSettingService.ListCameraRecordSettingsAsync(new ListCameraRecordSettingRequest
            {
                IsTakeAll = false,
                Page = page,
                Take = take
            });
            return Ok(result);
        }

        [HttpPost]
        [Authorize(Policy = "CameraSetting")]
        public async Task<IActionResult> CreateOrUpdateCameraSettingsAsync(
            CreateOrUpdateCameraRecordSettingRequest request)
        {
            var result = await _cameraSettingService.CreateOrUpdateCameraRecordSettingAsync(request);
            if (result.Success && request.IsActive)
            {
                var cameraRecordSetting = _cameraSettingService.GetActiveCameraRecordSetting(request.Name, request.Bus);
                if (request.BitRate != cameraRecordSetting.BitRate)
                {
                    _recordService.SetBitrate(cameraRecordSetting);
                }

                _ = Task.Run(async () => { await _recordService.Restart(cameraRecordSetting); });
            }

            return Ok(result);
        }

        [HttpDelete]
        [Authorize(Policy = "CameraSetting")]
        public async Task<IActionResult> DeleteCameraSettingAsync([FromQuery] Guid id)
        {
            var result = await _cameraSettingService.DeleteCameraRecordSettingAsync(id);
            return Ok(result);
        }

        [HttpPost("save-camera-focus")]
        [Authorize(Policy = "CameraSetting")]
        public async Task<IActionResult> SetCameraFocusAsync([FromBody] CameraChangeFocusRequest request)
        {
            var result = await _cameraSettingService.SetCameraFocusAsync(request);
            return Ok(result);
        }
    }
}