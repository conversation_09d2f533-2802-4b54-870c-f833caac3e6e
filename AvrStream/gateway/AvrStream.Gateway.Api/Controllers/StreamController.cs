using AvrStream.Gateway.Api.Dto;
using AvrStream.Gateway.Api.Services;
using Microsoft.AspNetCore.Mvc;

namespace AvrStream.Gateway.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class StreamController : ControllerBase
    {
        private readonly RecordService _recordService;

        public StreamController(RecordService recordService)
        {
            _recordService = recordService;
        }

        [HttpGet("list-streams")]
        public IActionResult ListStreamInfoAsync()
        {
            var streamInfos = new List<StreamInfoDto>();
            var recordPipeManagers = _recordService.RecordPipeManagers.FindAll(t => t.IsRunning);
            foreach (var recordPipeManager in recordPipeManagers)
            {
                var cam = recordPipeManager.RecordSetting.Cam;
                var mic = recordPipeManager.RecordSetting.Mic;
                var streamInfo = new StreamInfoDto
                {
                    Url = recordPipeManager.RecordSetting.RtspPart,
                    Devices = new List<AvrDeviceDto>
                    {
                        new AvrDeviceDto
                        {
                            DisplayName = cam.DisplayName,
                            Identifier = cam.Identifier,
                            Index = cam.Index,
                            Bus = cam.Bus
                        },
                        new AvrDeviceDto
                        {
                            DisplayName = mic.DisplayName,
                            Identifier = mic.Identifier,
                            Index = mic.Index,
                            Bus = mic.Bus
                        }
                    }
                };
                streamInfos.Add(streamInfo);
            }

            return Ok(streamInfos);
        }
    }
}