using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Models.Responses;
using AvrStream.Gateway.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AvrStream.Gateway.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DeviceController : ControllerBase
    {
        private readonly DeviceService _deviceService;

        public DeviceController(DeviceService deviceService)
        {
            _deviceService = deviceService;
        }

        [HttpGet("list-devices")]
        [Authorize(Policy = "GetDevice")]
        public IActionResult GetDevicesAsync()
        {
            var avrDevices = _deviceService.ListAvrDevicesAsync();
            return Ok(new DeviceResponse
            {
                AvrDevices = avrDevices,
                Success = true
            });
        }

        [HttpGet("list-cameras")]
        [Authorize(Policy = "GetDevice")]
        public IActionResult GetCameraDevices()
        {
            var devices = _deviceService.ListAvrCamerasAsync();
            return Ok(devices);
        }

        [HttpGet("list-camera-setting")]
        [Authorize]
        public async Task<IActionResult> GetCameraDeviceAndSettingAsync()
        {
            var result = await _deviceService.ListAvrCameraAndSettingAsync();
            return Ok(result);
        }
        
        [HttpPost("change-camera-focus")]
        [Authorize]
        public IActionResult SetCameraFocus([FromBody] CameraChangeFocusRequest request)
        {
            var result = _deviceService.SetCameraFocus(request);
            return Ok(result);
        }
    }
}
