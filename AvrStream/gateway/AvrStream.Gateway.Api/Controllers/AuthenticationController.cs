using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using AvrStream.Base.Jwt;
using AvrStream.Gateway.Api.Dto;
using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Models.Responses;
using AvrStream.Gateway.Entities.Databases;
using AvrStream.Gateway.Entities.Models;
using Microsoft.EntityFrameworkCore;
using AvrStream.Gateway.Api.Models;

namespace AvrStream.Gateway.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthenticationController : ControllerBase
    {
        private readonly Jwt _jwt;
        private readonly Token _token;
        private readonly UserManager<AppUser> _userManager;
        private readonly UserDbContext _db;


        public AuthenticationController(Jwt jwt, UserManager<AppUser> userManager, Token token, UserDbContext db)
        {
            _jwt = jwt;
            _userManager = userManager;
            _token = token;
            _db = db;
        }


        [HttpGet("account")]
        [Authorize]
        public async Task<IActionResult> GetAccountAsync()
        {
            var token = await HttpContext.GetTokenAsync("access_token");
            if (string.IsNullOrWhiteSpace(token))
            {
                return BadRequest("access_token is invalid");
            }
            var claimsPrincipal = _jwt.DecodeJwtToken(token);
            var username = claimsPrincipal.Claims.First(c => c.Type == ClaimTypes.NameIdentifier).Value;
            var user = await _userManager.FindByNameAsync(username);
            return Ok(new UserDto
            {
                Name = user?.Name
            });
        }

        [HttpPost("login")]
        [Consumes("application/x-www-form-urlencoded")]
        public async Task<IActionResult> LoginAsync([FromForm] LoginRequest request)
        {
            var user = await _userManager.FindByNameAsync(request.Username);
            if (user == null)
            {
                return Ok(new LoginResponse
                {
                    Success = false,
                    Message = "User does not exist"
                });
            }
            var checkPassword = await _userManager.CheckPasswordAsync(user, request.Password);
            if (!checkPassword)
            {
                return Ok(new LoginResponse
                {
                    Success = false,
                    Message = "Password is incorrect"
                });
            }
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id)
            };
            var role = await _db.UserRoles.Where(u => u.UserId == user.Id).FirstOrDefaultAsync();
            var permission = new Permission();
            if (role != null)
            {
                var roleClaims = await _db.RoleClaims.Where(x => x.RoleId == role.RoleId).ToListAsync();
                foreach (var claim in roleClaims)
                {
                    if (claim.ClaimType == "CreateAccount" && claim.ClaimValue == "True")
                    {
                        permission.CreateAccount = true;
                        claims.Add(new Claim("CreateAccount", "True"));
                    }
                    else if (claim.ClaimType == "UpdateAccount" && claim.ClaimValue == "True")
                    {
                        permission.UpdateAccount = true;
                        claims.Add(new Claim("UpdateAccount", "True"));
                    }
                    else if (claim.ClaimType == "DeleteAccount" && claim.ClaimValue == "True")
                    {
                        permission.DeleteAccount = true;
                        claims.Add(new Claim("DeleteAccount", "True"));
                    }
                    else if (claim.ClaimType == "GetAccount" && claim.ClaimValue == "True")
                    {
                        permission.GetAccount = true;
                        claims.Add(new Claim("GetAccount", "True"));
                    }
                    else if (claim.ClaimType == "ResetPassword" && claim.ClaimValue == "True")
                    {
                        permission.ResetPassword = true;
                        claims.Add(new Claim("ResetPassword", "True"));
                    }
                    else if (claim.ClaimType == "UpdatePermission" && claim.ClaimValue == "True")
                    {
                        permission.UpdatePermission = true;
                        claims.Add(new Claim("UpdatePermission", "True"));
                    }
                    else if (claim.ClaimType == "CreateRole" && claim.ClaimValue == "True")
                    {
                        permission.CreateRole = true;
                        claims.Add(new Claim("CreateRole", "True"));
                    }
                    else if (claim.ClaimType == "UpdateRole" && claim.ClaimValue == "True")
                    {
                        permission.UpdateRole = true;
                        claims.Add(new Claim("UpdateRole", "True"));
                    }
                    else if (claim.ClaimType == "DeleteRole" && claim.ClaimValue == "True")
                    {
                        permission.DeleteRole = true;
                        claims.Add(new Claim("DeleteRole", "True"));
                    }
                    else if (claim.ClaimType == "GetRole" && claim.ClaimValue == "True")
                    {
                        permission.GetRole = true;
                        claims.Add(new Claim("GetRole", "True"));
                    }
                    else if (claim.ClaimType == "GetDevice" && claim.ClaimValue == "True")
                    {
                        permission.GetDevice = true;
                        claims.Add(new Claim("GetDevice", "True"));
                    }
                    else if (claim.ClaimType == "UpdateDevice" && claim.ClaimValue == "True")
                    {
                        permission.UpdateDevice = true;
                        claims.Add(new Claim("UpdateDevice", "True"));
                    }
                    else if (claim.ClaimType == "Preview" && claim.ClaimValue == "True")
                    {
                        permission.Preview = true;
                        claims.Add(new Claim("Preview", "True"));
                    }
                    else if (claim.ClaimType == "GetRecordingFile" && claim.ClaimValue == "True")
                    {
                        permission.GetRecordingFile = true;
                        claims.Add(new Claim("GetRecordingFile", "True"));
                    }
                    else if (claim.ClaimType == "DeleteRecordingFile" && claim.ClaimValue == "True")
                    {
                        permission.DeleteRecordingFile = true;
                        claims.Add(new Claim("DeleteRecordingFile", "True"));
                    }
                    else if (claim.ClaimType == "CameraSetting" && claim.ClaimValue == "True")
                    {
                        permission.CameraSetting = true;
                        claims.Add(new Claim("CameraSetting", "True"));
                    }
                    else if (claim.ClaimType == "SystemSetting" && claim.ClaimValue == "True")
                    {
                        permission.SystemSetting = true;
                        claims.Add(new Claim("SystemSetting", "True"));
                    }
                }
            }

            
            var token = _token.CreateToken(claims);
            return Ok(new LoginResponse
            {
                Success = true,
                UserId = user.Id,
                Token = token,
                Name = user.Name,
                Permission = permission
            });
        }
    }
}