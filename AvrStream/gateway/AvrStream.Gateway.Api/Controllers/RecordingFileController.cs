using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Services;
using AvrStream.Gateway.Entities.Databases;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AvrStream.Gateway.Api.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public class RecordingFileController : ControllerBase
{
    private readonly RecordingFileService _fileService;
    public RecordingFileController(RecordingFileService fileService)
    {
        _fileService = fileService;
    }
    
    [HttpGet("list-recording-files")]
    [Authorize(Policy = "GetRecordingFile")]
    public async Task<IActionResult> ListRecordingFilesAsync([FromQuery] int page = 1, [FromQuery] int take = 10, [FromQuery] bool isTakeAll = false, 
        [FromQuery] DateTime? fromDate = null , [FromQuery] DateTime? endDate = null)
    {
        var result = await _fileService.ListRecordingFilesAsync(new ListRecordingFileRequest
        {
            Page = page,
            Take = take,
            IsTakeAll = isTakeAll,
            FromDate = fromDate,
            EndDate = endDate
        });
        return Ok(result);
    }

    [HttpDelete]
    [Authorize(Policy = "DeleteRecordingFile")]
    public async Task<IActionResult> DeleteRecordingFileAsync(Guid recordingId)
    {
        var result = await _fileService.DeleteRecordingFileAsync(recordingId);
        return Ok(result);
    }
}