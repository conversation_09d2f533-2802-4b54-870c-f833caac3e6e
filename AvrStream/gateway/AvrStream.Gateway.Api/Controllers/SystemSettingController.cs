using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Security.Cryptography;
using System.Text.RegularExpressions;
using AvrStream.Base.Cryptor;
using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Models.Responses;
using AvrStream.Gateway.Api.Services;
using AvrStream.Gateway.Entities.Models;
using Microsoft.AspNetCore.Mvc;

namespace AvrStream.Gateway.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SystemSettingController : ControllerBase
    {
        private readonly SystemConfigurationService _systemConfigurationService;
        private readonly RecordService _recordService;
        private readonly ClientService _clientService;
        private readonly ILogger<SystemSettingController> _logger;
        private readonly IConfiguration _configuration;
        private readonly MessageService _messageService;

        public SystemSettingController(SystemConfigurationService systemConfigurationService,
            RecordService recordService,
            ClientService clientService,
            ILogger<SystemSettingController> logger,
            IConfiguration configuration,
            MessageService messageService)
        {
            _systemConfigurationService = systemConfigurationService;
            _recordService = recordService;
            _clientService = clientService;
            _logger = logger;
            _configuration = configuration;
            _messageService = messageService;
        }

        [HttpGet]
        public async Task<IActionResult> GetSystemSettingAsync()
        {
            var systemSetting = await _systemConfigurationService.GetSystemSettingDtoAsync();
            return Ok(systemSetting);
        }

        [HttpGet("new-password")]
        public IActionResult GenerateNewPassword()
        {
            byte[] byteArray = new byte[32];
            using RandomNumberGenerator rng = RandomNumberGenerator.Create();
            rng.GetBytes(byteArray); // Fills the array with cryptographically secure random bytes
            string password = byteArray.ConvertByteArrayToHexString();
            return Ok(new PasswordResponse
            {
                Success = true,
                Password = password,
                Message = "Tạo mật khẩu mới thành công"
            });
        }

        [HttpPost]
        public async Task<IActionResult> UpdateSystemSettingAsync(UpdateSystemSettingRequest request)
        {
            var oldSystemSetting = await _systemConfigurationService.GetSystemSettingAsync();
            var result = await _systemConfigurationService.UpdateSystemSettingAsync(request);
            if (result.Success)
            {
                try
                {
                    var systemSetting = await _systemConfigurationService.GetSystemSettingAsync();
                    _recordService.SysSetting = systemSetting;
                    if (oldSystemSetting.IsNoiseProcessing != systemSetting.IsNoiseProcessing ||
                        oldSystemSetting.NoiseProcessingLevel != systemSetting.NoiseProcessingLevel)
                    {
                        _recordService.UpdateNoiseProcessing(request.IsNoiseProcessing, request.NoiseProcessingLevel);
                        _messageService.CreateMessage("Noise processing changed successfully", MessageType.Alert);
                    }

                    if (oldSystemSetting.IsEchoProcessing != systemSetting.IsEchoProcessing ||
                        oldSystemSetting.EchoProcessingLevel != systemSetting.EchoProcessingLevel)
                    {
                        _recordService.UpdateEchoProcessing(request.IsEchoProcessing, request.EchoProcessingLevel);
                        _messageService.CreateMessage("Echo processing changed successfully", MessageType.Alert);
                    }

                    if (oldSystemSetting.ServerUrl != systemSetting.ServerUrl)
                    {
                        _messageService.CreateMessage("Server URL has changed, please restart the system",
                            MessageType.Alert);
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError(e, null);
                }

                try
                {
                    var systemSetting = await _systemConfigurationService.GetSystemSettingAsync();
                    var ips = NetworkInterface
                        .GetAllNetworkInterfaces()
                        .Where(t => (t.NetworkInterfaceType == NetworkInterfaceType.Ethernet ||
                                     t.NetworkInterfaceType == NetworkInterfaceType.Wireless80211)
                                    && t.OperationalStatus == OperationalStatus.Up)
                        .SelectMany(t => t.GetIPProperties().UnicastAddresses)
                        .Where(t => t.Address.AddressFamily == AddressFamily.InterNetwork &&
                                    !IPAddress.IsLoopback(t.Address))
                        .Select(t => t.Address)
                        .ToList();
                    var ip = ips.FirstOrDefault()?.ToString();
                    var file = _configuration.GetValue<string>("UniqueFilePath");
                    var uniqueId = await System.IO.File.ReadAllTextAsync(file);
                    var pattern = @"[^a-zA-Z0-9_-]";
                    uniqueId = Regex.Replace(uniqueId, pattern, "");
                    var updateServerResult = await _clientService.UpdateBoxAsync(new CreateOrUpdateBoxRequest
                    {
                        IpAddress = ip,
                        Name = request.Name,
                        UniqueId = uniqueId,
                        UrlStream1 = systemSetting.StreamUrl1,
                        UrlStream2 = systemSetting.StreamUrl2
                    });
                    if (updateServerResult.Success)
                    {
                        _logger.LogInformation($"Update SystemSetting: {updateServerResult.Message}");
                        _messageService.CreateMessage(updateServerResult.Message, MessageType.Info);
                    }
                    else
                    {
                        _logger.LogError($"Update SystemSetting: {updateServerResult.Message}");
                        _messageService.CreateMessage(updateServerResult.Message, MessageType.Error);
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError(e, null);
                    _messageService.CreateMessage(e.Message, MessageType.Error);
                }
            }

            return Ok(result);
        }
    }
}