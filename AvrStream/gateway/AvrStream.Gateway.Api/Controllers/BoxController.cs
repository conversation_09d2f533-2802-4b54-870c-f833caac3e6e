using System.Diagnostics;
using AvrStream.Gateway.Api.Models.Responses;
using AvrStream.Gateway.Api.Services;
using Microsoft.AspNetCore.Mvc;

namespace AvrStream.Gateway.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BoxController : ControllerBase
    {
        private readonly RecordService _recordService;
        private readonly ILogger<BoxController> _logger;

        public BoxController(RecordService recordService, ILogger<BoxController> logger)
        {
            _recordService = recordService;
            _logger = logger;
        }

        [HttpGet("start/{cameraId}")]
        public async Task<IActionResult> Start(string cameraId)
        {
            try
            {
                var res = await _recordService.Start(cameraId);
                if (!res)
                {
                    return Ok(new BaseResponse
                    {
                        Success = false, // Fixed: was incorrectly true
                        Message = $"Start recording device {cameraId} failed"
                    });
                }

                return Ok(new BaseResponse
                {
                    Success = true,
                    Message = $"Started recording device {cameraId} successfully"
                });
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error starting camera {CameraId}: {Message}", cameraId, e.Message);
                return Ok(new BaseResponse
                {
                    Success = false,
                    Message = $"Error starting recording device {cameraId}: {e.Message}"
                });
            }
        }

        [HttpGet("startall")]
        public async Task<IActionResult> StartAll()
        {
            try
            {
                await _recordService.StartAll();
                return Ok(new BaseResponse
                {
                    Success = true,
                    Message = "Started recording for all cameras"
                });
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error starting all cameras: {Message}", e.Message);
                return Ok(new BaseResponse
                {
                    Success = false,
                    Message = $"Error starting all cameras: {e.Message}"
                });
            }
        }

        [HttpGet("stop/{cameraId}")]
        public IActionResult Stop(string cameraId)
        {
            try
            {
                var res = _recordService.Stop(cameraId);
                if (!res)
                {
                    return Ok(new BaseResponse
                    {
                        Success = false,
                        Message = $"Stopped recording device {cameraId} failed"
                    });
                }

                return Ok(new BaseResponse
                {
                    Success = true,
                    Message = $"Stopped recording device {cameraId} successfully"
                });
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error stopping camera {CameraId}: {Message}", cameraId, e.Message);
                return Ok(new BaseResponse
                {
                    Success = false,
                    Message = $"Error stopping recording device {cameraId}: {e.Message}"
                });
            }
        }

        [HttpGet("restart/{cameraId}")]
        public async Task<IActionResult> Restart(string cameraId)
        {
            try
            {
                var res = await _recordService.Restart(cameraId);
                if (!res)
                {
                    return Ok(new BaseResponse
                    {
                        Success = false,
                        Message = $"Failed to restart recording for camera {cameraId}"
                    });
                }

                return Ok(new BaseResponse
                {
                    Success = true,
                    Message = $"Restarted recording for camera {cameraId} successfully"
                });
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error restarting camera {CameraId}: {Message}", cameraId, e.Message);
                return Ok(new BaseResponse
                {
                    Success = false,
                    Message = $"Error restarting recording for camera {cameraId}: {e.Message}"
                });
            }
        }

        [HttpGet("reset-box")]
        public IActionResult ResetBox()
        {
            _recordService.StopAll();
            var res = RestartSystem();

            return Ok(new BaseResponse
            {
                Success = res,
                Message = res ? "Box is restarting, please wait" : "Box reboot failed"
            });
        }

        [HttpGet("force-reset-box")]
        public IActionResult ForceResetBox()
        {
            Task.Run(RestartSystem);
            return Ok(new BaseResponse
            {
                Success = true,
                Message = "Box is restarting, please wait"
            });
        }

        private bool RestartSystem()
        {
            try
            {
                var info = new ProcessStartInfo
                {
                    FileName = "/bin/bash",
                    Arguments = "-c \"reboot\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(info);

                if (process == null)
                {
                    _logger.LogInformation("Failed to start the reboot process.");
                    return false;
                }

                // Wait for the process to exit (with a timeout for safety)
                if (!process.WaitForExit(10000)) // 10 seconds timeout
                {
                    process.Kill(); // Force kill if it takes too long
                    _logger.LogInformation("Reboot command timed out and was killed.");
                    return false;
                }

                // Check exit code (0 usually indicates success)
                if (process.ExitCode == 0)
                {
                    _logger.LogInformation("Reboot command executed successfully.");
                    return true;
                }

                // Log any error output
                var error = process.StandardError.ReadToEnd();
                if (!string.IsNullOrEmpty(error))
                {
                    _logger.LogInformation("Error during reboot: " + error);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogInformation("An error occurred while attempting to reboot: " + ex.Message);
                return false;
            }
        }
    }
}