using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AvrStream.Gateway.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class RoleController : ControllerBase
    {
        private readonly RoleService _roleService;

        public RoleController(RoleService roleService)
        {
            _roleService = roleService;
        }

        [HttpGet("list-roles")]
        [Authorize(Policy = "GetRole")]
        public async Task<IActionResult> ListRolesAsync(int page = 0, int take = 10, bool isTakeAll = false)
        {
            if (page < 1)
            {
                page = 1;
            }

            var result = await _roleService.ListRolesAsync(new ListRoleRequest
            {
                IsTakeAll = isTakeAll,
                Take = take,
                Page = page
            });
            return Ok(result);
        }

        [HttpPost("create-role")]
        [Authorize(Policy = "CreateRole")]
        public async Task<IActionResult> CreateRole(CreateOrUpdateRoleRequest request)
        {
            var result = await _roleService.CreateRoleAsync(request);
            return Ok(result);
        }

        [HttpPut("update-role")]
        [Authorize(Policy = "UpdateRole")]
        public async Task<IActionResult> UpdateRole(CreateOrUpdateRoleRequest request)
        {
            var result = await _roleService.UpdateRoleAsync(request);
            return Ok(result);
        }

        [HttpDelete("delete-role")]
        [Authorize(Policy = "DeleteRole")]
        public async Task<IActionResult> DeleteRole([FromQuery] string roleId)
        {
            var result = await _roleService.DeleteRoleAsync(roleId);
            return Ok(result);
        }
    }
}
