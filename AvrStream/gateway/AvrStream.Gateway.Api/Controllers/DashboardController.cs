using AvrStream.Gateway.Api.Models;
using Microsoft.AspNetCore.Mvc;

namespace AvrStream.Gateway.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DashboardController : ControllerBase
    {
        public static readonly DashboardInfo DashboardInfo = new DashboardInfo();
        [HttpGet]
        [Route("[action]")]
        public IActionResult GetDashboardInfo()
        {
            return Ok(DashboardInfo);
        }
    }
}
