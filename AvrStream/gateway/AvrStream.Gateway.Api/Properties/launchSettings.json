{"$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:55423", "sslPort": 44392}}, "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "http://localhost:5045", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "https://localhost:7292;http://localhost:5045", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}