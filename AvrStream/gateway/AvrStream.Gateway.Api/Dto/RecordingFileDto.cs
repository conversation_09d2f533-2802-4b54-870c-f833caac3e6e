namespace AvrStream.Gateway.Api.Dto;

public class RecordingFileDto
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Password { get; set; }
    public string Iv { get; set; }
    public bool IsEncrypt { get; set; }
    public bool EncryptDone { get; set; }
    public string Path { get; set; }
    public bool IsEndRecording { get; set; }
    public bool IsSynchronized { get; set; }
    public DateTime? SynchronizedDate { get; set; }
    public DateTime CreatedDate { get; set; }
    public string VideoUrl { get; set; }
}