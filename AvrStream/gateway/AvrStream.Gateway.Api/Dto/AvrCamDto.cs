using System.Diagnostics;
using System.Runtime.InteropServices;
using AvrStream.Base.Streamer.Devices;

namespace AvrStream.Gateway.Api.Dto;

public class AvrCamDto
{
    public string DisplayName { get; set; }
    public string Identifier { get; set; }
    public int Index { get; set; }
    public string Bus { get; set; }
    public List<AvrVideoCap> Caps { get; set; }
    public AvrCamFocus Focus { get; set; }
    public void SetFocus(int value)
    {
        if (Focus.Identifier == null)
        {
            return;
        }

        if (value > 0)
        {
            Focus.Focus = 0;
            Focus.Value = value;
        }
        else
        {
            Focus.Focus = 1;
        }

        var info1 = new ProcessStartInfo("v4l2-ctl")
        {
            RedirectStandardOutput = true,
            Arguments = RuntimeInformation.RuntimeIdentifier.Contains("ubuntu.18.04-arm64")
                ? $"-d {Focus.Identifier} -c focus_auto={Focus.Focus}"
                : $"-d {Focus.Identifier} -c focus_automatic_continuous={Focus.Focus}"
        };

        var process1 = Process.Start(info1);
        if (false == process1!.WaitForExit(1000)) // Wait for 1 seconds
        {
            process1.Kill();
        }

        if (Focus.Focus == 0)
        {
            var info2 = new ProcessStartInfo("v4l2-ctl",
                $"-d {Focus.Identifier} -c focus_absolute={Focus.Value}")
            {
                RedirectStandardOutput = true
            };

            var process2 = Process.Start(info2);
            if (false == process2!.WaitForExit(1000)) // Wait for 1 seconds
            {
                process2.Kill();
            }
        }
    }
}