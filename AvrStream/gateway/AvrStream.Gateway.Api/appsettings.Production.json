{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "None"}}, "Jwt": {"Key": "LHPfBVbaQtnwvWhhd5YVaPK4ygVAJbab", "ExpiresInDays": 5, "ExpiresInHours": 4}, "ConnectionStrings": {"GatewayUser": "Server=localhost;Port=5432;Database=GatewayUser_dev;Username=postgres;Password=yourStrong(!!)Pa$$word;", "Gateway": "Server=localhost;Port=5432;Database=Gateway_dev;Username=postgres;Password=yourStrong(!!)Pa$$word;"}, "SystemSetting": {"Password": "1087A1FA26607864040F0DFB9DA51E630C7DF3948D5FC5F8789C85890383A017", "Iv": "8D8BF1DA5047BE5DB00305B0714838AF", "IsEncrypted": true, "SplitAfterMb": 300, "MaxFileLengthSeconds": 1000, "ServerUrl": "http://**************:9000", "RecordPath": "/mnt/ssd/avr-data", "StreamUrl1": "stream1", "StreamUrl2": "stream2", "IsNoiseProcessing": false, "NoiseProcessingLevel": 1, "IsEchoProcessing": false, "EchoProcessingLevel": 1, "Name": "<PERSON><PERSON><PERSON><PERSON> bị ghi âm ghi hình"}, "Serilog": {"Using": ["Serilog.Sinks.File"], "MinimumLevel": "Information", "WriteTo": [{"Name": "File", "Args": {"path": "/mnt/ssd/avr-data/Logs/GatewayApi-log-.txt", "fileSizeLimitBytes": "20971520", "rollOnFileSizeLimit": true, "rollingInterval": "Day", "retainedFileCountLimit": 30}}]}, "UniqueFilePath": "/proc/device-tree/serial-number"}