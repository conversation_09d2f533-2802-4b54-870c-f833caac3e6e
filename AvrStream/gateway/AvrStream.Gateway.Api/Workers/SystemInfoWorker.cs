using AvrStream.Gateway.Api.Controllers;
using AvrStream.Gateway.Api.Dto;
using AvrStream.Gateway.Api.Hub;
using AvrStream.Gateway.Api.Models;
using AvrStream.Gateway.Api.Services;
using Microsoft.AspNetCore.SignalR;
using Task = System.Threading.Tasks.Task;

namespace AvrStream.Gateway.Api.Workers;

public class SystemInfoWorker : IHostedService
{
    private readonly IHubContext<DashboardHub, IDashboardHubClient> _hubContext;
    private readonly ILogger<SystemInfoWorker> _logger;
    private readonly IConfiguration _configuration;
    private readonly SystemConfigurationService _systemConfigurationService;
    private readonly MessageService _messageService;
    private readonly ResourceService _resourceService;
    private readonly RecordService _recordService;
    private readonly DeviceService _deviceService;

    public SystemInfoWorker(ILogger<SystemInfoWorker> logger, IConfiguration configuration,
        IHubContext<DashboardHub, IDashboardHubClient> hubContext,
        SystemConfigurationService systemConfigurationService,
        MessageService messageService, ResourceService resourceService,
        RecordService recordService, DeviceService deviceService)
    {
        _logger = logger;
        _configuration = configuration;
        _hubContext = hubContext;
        _systemConfigurationService = systemConfigurationService;
        _messageService = messageService;
        _resourceService = resourceService;
        _recordService = recordService;
        _deviceService = deviceService;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("SystemInfoWorker is started.");
        Task.Run(async () =>
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    GetResource(cancellationToken);
                    GetDeviceStatus();
                    await GetMessagesAsync();
                    await _hubContext.Clients.All.SendDashboardInfo(DashboardController.DashboardInfo);
                    await Task.Delay(TimeSpan.FromSeconds(10), cancellationToken);
                }
                catch (Exception e)
                {
                    _logger.LogError(e, null);
                }
            }
        }, cancellationToken);
        return Task.CompletedTask;
    }

    private void GetResource(CancellationToken cancellationToken)
    {
        Task.Run(async () =>
        {
            try
            {
                var path = "";
                var systemSetting = await _systemConfigurationService.GetSystemSettingAsync();
                if (systemSetting != null)
                {
                    path = systemSetting.RecordPath;
                }

                if (string.IsNullOrWhiteSpace(path))
                {
                    path = _configuration.GetValue<string>("SystemSetting:RecordPath");
                }

                var cpu = Math.Round(_resourceService.GetCpu(), 2);
                var memory = Math.Round(_resourceService.GetMemory(), 2);
                var diskUsed = _resourceService.GetDiskUsed(path);
                var diskTotal = _resourceService.GetDiskTotal(path);
                var cpuTemp = _resourceService.GetCpuTemperature();
                if (cpu >= 0)
                {
                    DashboardController.DashboardInfo.Cpu = cpu;
                }

                DashboardController.DashboardInfo.Memory = memory;
                DashboardController.DashboardInfo.DiskUsed = diskUsed;
                DashboardController.DashboardInfo.DiskTotal = diskTotal;
                DashboardController.DashboardInfo.CpuTemp = cpuTemp;
            }
            catch (Exception e)
            {
                _logger.LogError(e, null);
            }
        }, cancellationToken);
    }

    private void GetDeviceStatus()
    {
        try
        {
            DashboardController.DashboardInfo.StatusCam1 = DeviceStatus.NotFound;
            DashboardController.DashboardInfo.StatusCam2 = DeviceStatus.NotFound;
            DashboardController.DashboardInfo.StatusMic1 = DeviceStatus.NotFound;
            DashboardController.DashboardInfo.StatusMic2 = DeviceStatus.NotFound;
            _deviceService.ListAvrDevicesAsync();
            if (_deviceService.Cam1 != null)
            {
                DashboardController.DashboardInfo.StatusCam1 = DeviceStatus.Recognized;
            }

            if (_deviceService.Cam2 != null)
            {
                DashboardController.DashboardInfo.StatusCam2 = DeviceStatus.Recognized;
            }

            if (_deviceService.Mic1 != null)
            {
                DashboardController.DashboardInfo.StatusMic1 = DeviceStatus.Recognized;
            }

            if (_deviceService.Mic2 != null)
            {
                DashboardController.DashboardInfo.StatusMic2 = DeviceStatus.Recognized;
            }

            var recordPipeManagers = _recordService.RecordPipeManagers.FindAll(t => t.IsRunning);
            foreach (var recordPipeManager in recordPipeManagers)
            {
                if (recordPipeManager.PipeName == "cam1")
                {
                    DashboardController.DashboardInfo.StatusCam1 = DeviceStatus.Recording;
                    DashboardController.DashboardInfo.StatusMic1 = DeviceStatus.Recording;
                }
                else if (recordPipeManager.PipeName == "cam2")
                {
                    DashboardController.DashboardInfo.StatusCam2 = DeviceStatus.Recording;
                    DashboardController.DashboardInfo.StatusMic2 = DeviceStatus.Recording;
                }
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, null);
        }
    }

    private async Task GetMessagesAsync()
    {
        try
        {
            var messages = await _messageService.ListLastMessagesAsync(50);
            DashboardController.DashboardInfo.Messages = new List<MessageDto>();
            foreach (var message in messages)
            {
                DashboardController.DashboardInfo.Messages.Add(new MessageDto
                {
                    Id = message.Id,
                    MessageType = message.MessageType,
                    Message = message.Message,
                    CreatedDate = message.CreatedDate
                });
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, null);
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("SystemInfoWorker is stopped.");
        return Task.CompletedTask;
    }
}