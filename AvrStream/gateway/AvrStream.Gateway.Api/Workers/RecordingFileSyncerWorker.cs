using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text.RegularExpressions;
using AvrStream.Base.Cryptor;
using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Services;
using AvrStream.Gateway.Entities.Models;
using TusDotNetClient;

namespace AvrStream.Gateway.Api.Workers
{
    public class RecordingFileSyncerWorker : BackgroundService
    {
        private readonly ILogger<RecordingFileSyncerWorker> _logger;
        private readonly IConfiguration _configuration;
        private readonly RecordingFileService _recordingFileService;
        private readonly SystemConfigurationService _systemConfigurationService;
        private readonly ClientService _clientService;
        private readonly MessageService _messageService;

        public RecordingFileSyncerWorker(
            ILogger<RecordingFileSyncerWorker> logger,
            IConfiguration configuration,
            RecordingFileService recordingFileService,
            SystemConfigurationService systemConfigurationService,
            ClientService clientService,
            MessageService messageService)
        {
            _logger = logger;
            _configuration = configuration;
            _recordingFileService = recordingFileService;
            _systemConfigurationService = systemConfigurationService;
            _clientService = clientService;
            _messageService = messageService;
        }

        protected override async Task ExecuteAsync(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    var filesRecord = await _recordingFileService.GetSyncRecordingFilesAsync();
                    foreach (var fileRecord in filesRecord)
                    {
                        // check file exists
                        if (!File.Exists(fileRecord.Path))
                        {
                            _logger.LogError($"File {fileRecord.Path} does not exists");
                            _messageService.CreateMessage($"File {fileRecord.Path} does not exist", MessageType.Error);
                            await _recordingFileService.DeleteRecordingFileAsync(fileRecord.Path);
                            continue;
                        }
                        // calculate file hash before uploading
                        fileRecord.Hash = FileHashHelper.CalculateFileSha512Hash(fileRecord.Path);
                        await _recordingFileService.UpdateRecordingFileAsync(fileRecord);
                        await UploadFileAsync(fileRecord, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "An error occurred while syncing files.");
                }

                await Task.Delay(TimeSpan.FromMinutes(1), cancellationToken); // Wait for 1 minute before checking again
            }
        }

        private async Task UploadFileAsync(RecordingFile recordFile, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation($"Begin upload file {recordFile.Path}");
                var systemSetting = await _systemConfigurationService.GetSystemSettingAsync();
                var file = _configuration.GetValue<string>("UniqueFilePath");
                var uniqueId = await File.ReadAllTextAsync(file, cancellationToken);
                var pattern = @"[^a-zA-Z0-9_-]";
                uniqueId = Regex.Replace(uniqueId, pattern, "");


                var authenticateBoxResponse = await _clientService.AuthenticateBoxAsync(new AuthenticateBoxRequest
                {
                    Username = uniqueId,
                    Password = uniqueId
                });
                if (authenticateBoxResponse.Token == null)
                {
                    var isRegister = await _clientService.CheckBoxRegisterAsync();
                    if (!isRegister.Success)
                    {
                        var name = systemSetting.Name;
                        if (string.IsNullOrWhiteSpace(systemSetting.Name))
                        {
                            name = _configuration.GetValue<string>("SystemSetting:Name");
                        }

                        var ips = NetworkInterface
                            .GetAllNetworkInterfaces()
                            .Where(t => (t.NetworkInterfaceType == NetworkInterfaceType.Ethernet ||
                                         t.NetworkInterfaceType == NetworkInterfaceType.Wireless80211)
                                        && t.OperationalStatus == OperationalStatus.Up)
                            .SelectMany(t => t.GetIPProperties().UnicastAddresses)
                            .Where(t => t.Address.AddressFamily == AddressFamily.InterNetwork &&
                                        !IPAddress.IsLoopback(t.Address))
                            .Select(t => t.Address)
                            .ToList();

                        // TODO: need check ip is correct
                        var ip = ips.FirstOrDefault()?.ToString();
                        var response = await _clientService.CreateBoxAsync(new CreateOrUpdateBoxRequest
                        {
                            Name = name,
                            IpAddress = ip,
                            UniqueId = uniqueId,
                            UrlStream1 = systemSetting.StreamUrl1,
                            UrlStream2 = systemSetting.StreamUrl2
                        });
                        if (!response.Success)
                        {
                            _logger.LogError("Failed to create box.");
                            _messageService.CreateMessage($"Error creating box on server", MessageType.Error);
                        }
                    }

                    return;
                }


                await using var fileStream = new FileStream(recordFile.Path, FileMode.Open, FileAccess.Read);
                var client = new TusClient
                    { AdditionalHeaders = { { "Authorization", $"Bearer {authenticateBoxResponse.Token}" } } };

                // get file name
                var fileInfo = new FileInfo(recordFile.Path);

                var fileUrl = $"{systemSetting.ServerUrl}/{uniqueId}/{recordFile.Name}";
                _logger.LogInformation("Head Async Started ...");
                var head = await client.HeadAsync(fileUrl);
                _logger.LogInformation("Head Async Ended ...");
                if (head.StatusCode == HttpStatusCode.NotFound)
                {
                    var response = await _clientService.CreateRecordingFileServerResponseAsync(
                        new CreateRecordingFileServerRequest
                        {
                            Name = recordFile.Name,
                            Password = recordFile.Password,
                            Iv = recordFile.Iv,
                            IsEncrypt = recordFile.IsEncrypt,
                            Hash = recordFile.Hash,
                            RecordingDate = recordFile.CreatedDate,
                            UniqueId = uniqueId,
                        });
                    if (response.Success)
                    {
                        recordFile.RecordingFileIdServer = response.RecordingFileId;
                        await _recordingFileService.UpdateRecordingFileAsync(recordFile);
                        _logger.LogInformation("Create Async Started ...");
                        _messageService.CreateMessage($"Creating file {recordFile.Name}", MessageType.Info);
                        fileUrl = await client.CreateAsync($"{systemSetting.ServerUrl}/files", fileInfo.Length,
                            ("uniqueId", $"{uniqueId}"),
                            ("recordingFileId", $"{response.RecordingFileId}"),
                            ("fileName", $"{recordFile.Name}"));
                        _logger.LogInformation("Create Async Ended ...");
                    }
                }

                _logger.LogInformation("Upload Async Started ...");
                _messageService.CreateMessage($"Synchronizing file {recordFile.Name}", MessageType.Info);
                var res = await client.UploadAsync(fileUrl,
                    File.OpenRead(recordFile.Path), 1.0,
                    cancellationToken);
                _logger.LogInformation("Upload Async Ended ...");
                var success = res.All(k => k.StatusCode == HttpStatusCode.NoContent);

                if (success)
                {
                    recordFile.IsSynchronized = true;
                    recordFile.SynchronizedDate = DateTime.Now;
                    await _recordingFileService.UpdateRecordingFileAsync(recordFile);
                    _logger.LogInformation($"Successfully uploaded file: {fileInfo.Name}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to upload file: {recordFile.Path}");
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("RecordingFileSyncerWorker is stopped.");
            await base.StopAsync(cancellationToken);
        }
    }
}