using System.Security.Cryptography;
using AvrStream.Base.Cryptor;
using AvrStream.Gateway.Api.Services;
using AvrStream.Gateway.Entities.Models;

namespace AvrStream.Gateway.Api.Workers
{
    public class EncryptWorker : BackgroundService
    {
        private readonly ILogger<EncryptWorker> _logger;
        private readonly RecordingFileService _recordingFileService;
        private readonly MessageService _messageService;


        public EncryptWorker(ILogger<EncryptWorker> logger, RecordingFileService recordingFileService, MessageService messageService)
        {
            _logger = logger;
            _recordingFileService = recordingFileService;
            _messageService = messageService;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var filesRecord = await _recordingFileService.GetUnEncryptRecordingFilesAsync();
                    foreach (var fileRecord in filesRecord)
                    {
                        await EncryptFileAsync(fileRecord, stoppingToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "An error occurred while encrypting files.");
                }

                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken); // Wait for 1 minute before checking again
            }
        }

        private async Task EncryptFileAsync(RecordingFile recordingFile, CancellationToken cancellationToken)
        {
            try
            {
                if (File.Exists(recordingFile.Path))
                {
                    _logger.LogInformation($"Encrypt file: {recordingFile.Path}");
                    _messageService.CreateMessage($"Encrypting file: {recordingFile.Path}", MessageType.Info);
                    var fileInfo = new FileInfo(recordingFile.Path);
                    await using var inputFileStream =
                        new FileStream(recordingFile.Path, FileMode.Open, FileAccess.Read);
                    // replace uncrypt folder to encrypt folder
                    var outputFilePath = fileInfo.FullName.Replace("unencrypt", "encrypt");
                    await using var outputFileStream =
                        new FileStream(outputFilePath, FileMode.Create, FileAccess.Write);
                    using var aes = Aes.Create();
                    aes.Key = recordingFile.Password.ConvertHexStringToByteArray();
                    aes.IV = recordingFile.Iv.ConvertHexStringToByteArray();
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;
                    // Create an encryptor to perform the stream transform.
                    await using var cryptoStream =
                        new CryptoStream(outputFileStream, aes.CreateEncryptor(), CryptoStreamMode.Write);
                    await inputFileStream.CopyToAsync(cryptoStream, cancellationToken);
                    File.Delete(recordingFile.Path);
                    recordingFile.EncryptDone = true;
                    recordingFile.Path = outputFilePath;
                    await _recordingFileService.UpdateRecordingFileAsync(recordingFile);
                }
                else
                {
                    _logger.LogError($"File {recordingFile.Path} does not exists");
                    _messageService.CreateMessage($"File {recordingFile.Path} does not exist", MessageType.Error);
                    await _recordingFileService.DeleteRecordingFileAsync(recordingFile.Path);
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "An error occurred while encrypting file.");
                _messageService.CreateMessage(e.Message, MessageType.Error);
            }
        }
        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("EncryptWorker is stopped.");
            await base.StopAsync(cancellationToken);
        }
    }
}