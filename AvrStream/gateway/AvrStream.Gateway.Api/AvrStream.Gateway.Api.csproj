<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.35" />
    <PackageReference Include="Serilog.AspNetCore" Version="6.1.0" />
    <PackageReference Include="TusClient" Version="1.0.5" />
    <PackageReference Include="TusDotNetClient" Version="1.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\base\AvrStream.Base.Cryptor\AvrStream.Base.Cryptor.csproj" />
    <ProjectReference Include="..\..\base\AvrStream.Base.Jwt\AvrStream.Base.Jwt.csproj" />
    <ProjectReference Include="..\..\base\AvrStream.Base.Models\AvrStream.Base.Models.csproj" />
    <ProjectReference Include="..\AvrStream.Gateway.Entities\AvrStream.Gateway.Entities.csproj" />
    <ProjectReference Include="..\AvrStream.Gateway.Streamer\AvrStream.Gateway.Streamer.csproj" />
  </ItemGroup>

</Project>
