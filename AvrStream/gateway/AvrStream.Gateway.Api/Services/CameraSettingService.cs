using AvrStream.Base.Models;
using AvrStream.Gateway.Api.Dto;
using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Models.Responses;
using AvrStream.Gateway.Entities.Databases;
using AvrStream.Gateway.Entities.Models;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Gateway.Api.Services;

public class CameraSettingService
{
    private readonly IServiceScopeFactory _scopeFactory;

    public CameraSettingService(IServiceScopeFactory scopeFactory)
    {
        _scopeFactory = scopeFactory;
    }

    public async Task<ListResult<CameraRecordSettingDto>> ListCameraRecordSettingsAsync(
        ListCameraRecordSettingRequest request)
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var query = db.CameraRecordSettings.AsQueryable();
        var total = await query.CountAsync();
        if (!request.IsTakeAll)
        {
            query = query.Skip((request.Page - 1) * request.Take).Take(request.Take);
        }

        var settings = await query.Select(s => new CameraRecordSettingDto
        {
            FocusValue = s.FocusValue,
            IsAutofocus = s.IsAutofocus,
            Fps = s.Fps,
            Id = s.Id,
            IsActive = s.IsActive,
            Name = s.Name,
            Resolution = s.Resolution,
            BitRate = s.BitRate,
            Bus = s.Bus
        }).ToListAsync();
        return new ListResult<CameraRecordSettingDto>(settings, total);
    }

    public CameraRecordSetting GetActiveCameraRecordSetting(string name, string bus)
    {
        using var scope = _scopeFactory.CreateScope();
        using var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        return db.CameraRecordSettings.FirstOrDefault(r => r.IsActive && r.Name == name && r.Bus == bus);
    }

    public async Task<CameraRecordSettingDto> GetActiveCameraRecordSettingAsync(string name, string bus)
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        return await db.CameraRecordSettings.Where(r => r.IsActive && r.Name == name && r.Bus == bus)
            .Select(r => new CameraRecordSettingDto
            {
                Bus = r.Bus,
                BitRate = r.BitRate,
                FocusValue = r.FocusValue,
                Fps = r.Fps,
                Id = r.Id,
                IsActive = r.IsActive,
                IsAutofocus = r.IsAutofocus,
                Name = r.Name,
                Resolution = r.Resolution
            }).SingleOrDefaultAsync();
    }

    public async Task<BaseResponse> CreateOrUpdateCameraRecordSettingAsync(
        CreateOrUpdateCameraRecordSettingRequest request)
    {
        var scope = _scopeFactory.CreateScope();
        var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        if (request.Id != null)
        {
            var setting = await db.CameraRecordSettings.SingleOrDefaultAsync(r => r.Id == request.Id);
            if (setting == null)
            {
                return new BaseResponse
                {
                    Success = false,
                    Message = "Configuration does not exist"
                };
            }

            if (setting.IsActive)
            {
                var oldSettingsActive = await db.CameraRecordSettings.Where(r =>
                    r.IsActive == true && r.Name == request.Name && r.Bus == request.Bus && r.Id != request.Id).ToListAsync();
                foreach (var recordSetting in oldSettingsActive)
                {
                    recordSetting.IsActive = false;
                    db.Update(recordSetting);
                }

                await db.SaveChangesAsync();
            }

            setting.Bus = request.Bus;
            setting.FocusValue = request.FocusValue;
            setting.IsAutofocus = request.IsAutoFocus;
            setting.Fps = request.Fps;
            setting.IsActive = request.IsActive;
            setting.Name = request.Name;
            setting.Resolution = request.Resolution;
            setting.BitRate = request.BitRate;
            db.Update(setting);
            await db.SaveChangesAsync();
        }
        else
        {
            if (request.IsActive)
            {
                var oldSettingsActive = await db.CameraRecordSettings.Where(r =>
                    r.IsActive == true && r.Name == request.Name && r.Bus == request.Bus ).ToListAsync();
                foreach (var recordSetting in oldSettingsActive)
                {
                    recordSetting.IsActive = false;
                    db.Update(recordSetting);
                }

                await db.SaveChangesAsync();
            }

            var newSetting = new CameraRecordSetting
            {
                FocusValue = request.FocusValue,
                IsAutofocus = request.IsAutoFocus,
                Fps = request.Fps,
                IsActive = request.IsActive,
                Name = request.Name,
                Resolution = request.Resolution,
                BitRate = request.BitRate,
                Bus = request.Bus
            };
            await db.AddAsync(newSetting);
            await db.SaveChangesAsync();
        }

        return new BaseResponse
        {
            Success = true,
            Message = "Configuration created successfully"
        };
    }


    public async Task<BaseResponse> DeleteCameraRecordSettingAsync(Guid id)
    {
        var scope = _scopeFactory.CreateScope();
        var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var setting = await db.CameraRecordSettings.SingleOrDefaultAsync(r => r.Id == id);
        if (setting == null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Configuration does not exist"
            };
        }

        if (setting.IsActive)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Cannot delete active configuration"
            };
        }

        db.Remove(setting);
        await db.SaveChangesAsync();
        return new BaseResponse
        {
            Success = true,
            Message = "Configuration deleted successfully"
        };
    }

    public async Task<BaseResponse> SetCameraFocusAsync(CameraChangeFocusRequest request)
    {
        var scope = _scopeFactory.CreateScope();
        var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var activeSetting = await db.CameraRecordSettings.SingleOrDefaultAsync(r => r.IsActive &&
            r.Name == request.Name && r.Bus == request.Bus);
        if (activeSetting == null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Settings for the current camera do not exist"
            };
        }

        activeSetting.FocusValue = request.FocusValue;
        activeSetting.IsAutofocus = request.IsAutoFocus;
        db.Update(activeSetting);
        await db.SaveChangesAsync();
        // update focus camera
        return new BaseResponse
        {
            Success = true,
            Message = "Camera focus update successful"
        };
    }
}