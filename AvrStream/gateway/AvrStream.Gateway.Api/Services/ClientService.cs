using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Models.Responses;

namespace AvrStream.Gateway.Api.Services;

public class ClientService
{
    private readonly SystemConfigurationService _systemConfigurationService;
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;

    public ClientService(IConfiguration configuration, SystemConfigurationService systemConfigurationService)
    {
        _systemConfigurationService = systemConfigurationService;
        _configuration = configuration;
        _httpClient = new HttpClient();
    }

    public async Task<AuthenticateBoxResponse> AuthenticateBoxAsync(AuthenticateBoxRequest request)
    {
        var systemSetting = await _systemConfigurationService.GetSystemSettingAsync();
        // Serialize the object to JSON
        var formData = new Dictionary<string, string>
        {
            { "Username", request.Username },
            { "Password", request.Password }
        };

        // Create the form content
        var content = new FormUrlEncodedContent(formData);
        var result = await _httpClient.PostAsync($"{systemSetting.ServerUrl}/api/Box/authenticate", content);
        var r = await result.Content.ReadAsStringAsync();
        if (result.IsSuccessStatusCode)
        {
            var response = JsonSerializer.Deserialize<AuthenticateBoxResponse>(r);
            _httpClient.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("Bearer", response.Token);
            return response;
        }
        else
        {
            return new AuthenticateBoxResponse
            {
                Success = false,
                Message = r
            };
        }
    }

    public async Task<BaseResponse> CreateBoxAsync(CreateOrUpdateBoxRequest request)
    {
        var systemSetting = await _systemConfigurationService.GetSystemSettingAsync();
        // Serialize the object to JSON
        var jsonData = JsonSerializer.Serialize(request);

        // Create the StringContent object with the JSON payload
        var content = new StringContent(jsonData, Encoding.UTF8, "application/json");
        try
        {
            var result = await _httpClient.PostAsync($"{systemSetting.ServerUrl}/api/Box/create", content);
            var r = await result.Content.ReadAsStringAsync();
            if (result.IsSuccessStatusCode)
            {
                var response = JsonSerializer.Deserialize<BaseResponse>(r);
                return response;
            }
            else
            {
                return new BaseResponse
                {
                    Success = false,
                    Message = r
                };
            }
        }
        catch
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Failed to connect to server"
            };
        }
    }

    public async Task<BaseResponse> CheckBoxRegisterAsync()
    {
        var file = _configuration.GetValue<string>("UniqueFilePath");
        var systemSetting = await _systemConfigurationService.GetSystemSettingAsync();
        var uniqueId = await File.ReadAllTextAsync(file);
        var pattern = @"[^a-zA-Z0-9_-]";
        uniqueId = Regex.Replace(uniqueId, pattern, "");
        try
        {
            var result = await _httpClient.GetAsync($"{systemSetting.ServerUrl}/api/Box/check-box-exists/{uniqueId}");
            var r = await result.Content.ReadAsStringAsync();
            if (result.IsSuccessStatusCode)
            {
                var response = JsonSerializer.Deserialize<BaseResponse>(r);
                if (response.Success)
                {
                    return new BaseResponse
                    {
                        Success = true,
                        Message = "Box already register"
                    };
                }
                else
                {
                    return new BaseResponse
                    {
                        Success = false,
                        Message = "Box does not register"
                    };
                }
            }

            return new BaseResponse
            {
                Success = false,
                Message = r
            };
        }
        catch
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Failed to connect to server"
            };
        }
    }

    public async Task<RecordingFileServerResponse> CreateRecordingFileServerResponseAsync(
        CreateRecordingFileServerRequest request)
    {
        var systemSetting = await _systemConfigurationService.GetSystemSettingAsync();
        // Serialize the object to JSON
        var jsonData = JsonSerializer.Serialize(request);

        // Create the StringContent object with the JSON payload
        var content = new StringContent(jsonData, Encoding.UTF8, "application/json");
        var result = await _httpClient.PostAsync($"{systemSetting.ServerUrl}/api/RecordingFile/create-recording-file",
            content);
        var r = await result.Content.ReadAsStringAsync();
        if (result.IsSuccessStatusCode)
        {
            var response = JsonSerializer.Deserialize<RecordingFileServerResponse>(r);
            return response;
        }
        else
        {
            return new RecordingFileServerResponse
            {
                Success = false,
                Message = r
            };
        }
    }

    public async Task<BaseResponse> UpdateBoxAsync(CreateOrUpdateBoxRequest request)
    {
        var systemSetting = await _systemConfigurationService.GetSystemSettingAsync();
        // Serialize the object to JSON
        var jsonData = JsonSerializer.Serialize(request);

        // Create the StringContent object with the JSON payload
        var content = new StringContent(jsonData, Encoding.UTF8, "application/json");
        try
        {
            var result = await _httpClient.PostAsync($"{systemSetting.ServerUrl}/api/Box/update-client", content);
            var r = await result.Content.ReadAsStringAsync();
            if (result.IsSuccessStatusCode)
            {
                var response = JsonSerializer.Deserialize<BaseResponse>(r);
                return response;
            }
            else
            {
                return new BaseResponse
                {
                    Success = false,
                    Message = r
                };
            }
        }
        catch
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Failed to connect to server"
            };
        }
    }
}