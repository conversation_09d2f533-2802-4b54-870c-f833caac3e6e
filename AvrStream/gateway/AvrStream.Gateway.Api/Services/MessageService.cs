using AvrStream.Base.Models;
using AvrStream.Gateway.Api.Dto;
using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Models.Responses;
using AvrStream.Gateway.Entities.Databases;
using AvrStream.Gateway.Entities.Models;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Gateway.Api.Services;

public class MessageService
{
    private readonly IServiceScopeFactory _scopeFactory;

    public MessageService(IServiceScopeFactory scopeFactory)
    {
        _scopeFactory = scopeFactory;
    }

    public async Task<ListResult<MessageDto>> ListMessagesAsync(ListMessageRequest request)
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var query = db.Messages.AsQueryable();
        var total = await query.CountAsync();
        if (!request.IsTakeAll)
        {
            query = query.Skip((request.Page - 1) * request.Take).Take(request.Take);
        }
        var messages = await query.OrderByDescending(x => x.CreatedDate).Select(x => new MessageDto
        {
            Id = x.Id,
            Message = x.Message,
            MessageType = x.MessageType,
            CreatedDate = x.CreatedDate
        }).ToListAsync();
        return new ListResult<MessageDto>(messages, total);
    }

    public async Task<List<MessageDto>> ListLastMessagesAsync(int take)
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var messages = await db.Messages.AsNoTracking()
            .OrderByDescending(m => m.CreatedDate).Take(take).Select(x =>
            new MessageDto
            {
                Message = x.Message,
                CreatedDate = x.CreatedDate,
                Id = x.Id,
                MessageType = x.MessageType
            }).ToListAsync();
        return messages;
    }

    public BaseResponse CreateMessage(string message, MessageType messageType)
    {
        try
        {
            var scope = _scopeFactory.CreateScope();
            var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
            db.Messages.Add(new BoxMessage
            {
                Message = message,
                MessageType = messageType
            });
            db.SaveChangesAsync();
            return new BaseResponse
            {
                Success = true
            };
        }
        catch (Exception e)
        {
            return new BaseResponse
            {
                Success = false,
                Message = e.Message
            };
        }
    }

    public async Task<BaseResponse> DeleteMessageAsync(Guid id)
    {
        var scope = _scopeFactory.CreateScope();
        var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var message = await db.Messages.SingleOrDefaultAsync(x => x.Id == id);
        if (message == null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Message does not exists"
            };
        }

        db.Remove(message);
        await db.SaveChangesAsync();
        return new BaseResponse
        {
            Success = true
        };
    }
}