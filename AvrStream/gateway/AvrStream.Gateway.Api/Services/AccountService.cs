using AvrStream.Base.Models;
using AvrStream.Gateway.Api.Dto;
using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Models.Responses;
using AvrStream.Gateway.Entities.Databases;
using AvrStream.Gateway.Entities.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Gateway.Api.Services;

public class AccountService
{
    private readonly UserDbContext _db;
    private readonly UserManager<AppUser> _userManager;
    private readonly RoleManager<IdentityRole> _roleManager;

    public AccountService(UserDbContext db, UserManager<AppUser> userManager, RoleManager<IdentityRole> roleManager)
    {
        _db = db;
        _userManager = userManager;
        _roleManager = roleManager;
    }

    public async Task<ListResult<AccountDto>> ListAccountsAsync(ListAccountRequest request)
    {
        var query = _db.Users.Where(u =>
            (string.IsNullOrWhiteSpace(request.Username) || u.NormalizedUserName == request.Username.ToUpper()) &&
            (string.IsNullOrWhiteSpace(request.Name) || EF.Functions.ToTsVector("english", u.Name).Matches(request.Name)));
        var total = await query.CountAsync();
        if (!request.IsTakeAll)
        {
            query = query.Skip((request.Page - 1) * request.Take).Take(request.Take);
        }

        var users = await query.OrderBy(u => u.UserName)
            .ThenBy(u => u.Name).ToListAsync();
        var userIds = users.Select(x => x.Id).ToList();
        var userRoles = await _db.UserRoles.Where(x => userIds.Contains(x.UserId)).ToListAsync();
        var userRoleIds = userRoles.Select(r => r.RoleId).ToList();
        var roles = await _db.Roles.Where(x => userRoleIds.Contains(x.Id))
            .ToListAsync();
        var accountInfos = new List<AccountDto>();
        foreach (var user in users)
        {
            var roleIds = userRoles.Where(x => x.UserId == user.Id).Select(x => x.RoleId).ToList();
            var listRoles = roles.Where(x => roleIds.Contains(x.Id)).Select(x => new RoleDto
            {
                Id = x.Id,
                Name = x.Name
            }).ToList();
            accountInfos.Add(new AccountDto
            {
                Id = user.Id,
                Name = user.Name,
                Username = user.UserName,
                Roles = listRoles
            });
        }

        return new ListResult<AccountDto>(accountInfos, total);
    }

    public async Task<BaseResponse> CreateAccountAsync(CreateAccountRequest request)
    {
        if (request.Password != request.ConfirmPassword)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Passwords do not match"
            };
        }
        var existsAccount = await _userManager.FindByNameAsync(request.Username);
        if (existsAccount != null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = $"Account {request.Username} already exists"
            };
        }

        var existsRole = await _db.Roles.AnyAsync(x => x.Id == request.RoleId);
        if (!existsRole)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Role doesn't exists"
            };
        }

        var user = new AppUser
        {
            Email = $"{request.Username}@gmail.com",
            UserName = request.Username,
            Name = request.Name
        };
        var result = await _userManager.CreateAsync(user, request.Password);
        if (result.Succeeded)
        {
            await _db.UserRoles.AddAsync(new IdentityUserRole<string>
            {
                UserId = user.Id,
                RoleId = request.RoleId
            });
            await _db.SaveChangesAsync();
            return new BaseResponse
            {
                Success = true,
                Message = "Create Account Successful"
            };
        }
        else
        {
            var strError = "";
            var errors = result.Errors.ToList();
            foreach (var error in errors)
            {
                if (error.Code == "PasswordTooShort")
                {
                    strError += "Password must be at least 5 characters";
                }

                if (error.Code == "InvalidUserName")
                {
                    strError += "Invalid account name";
                }

                strError += ", ";
            }

            if (strError.EndsWith(", "))
            {
                strError = strError.Substring(0, strError.Length - 2);
            }

            return new BaseResponse
            {
                Success = false,
                Message = strError
            };
        }
    }

    public async Task<BaseResponse> UpdateAccountAsync(UpdateAccountRequest request)
    {
        var user = await _userManager.FindByIdAsync(request.Id);
        if (user == null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Account does not exist"
            };
        }
        var existsUser = await _userManager.FindByNameAsync(request.Username);
        if (existsUser != null && existsUser.Id != user.Id)
        {
            return new BaseResponse
            {
                Success = false,
                Message = $"An account named {request.Username} already exists"
            };
        }

        var oldRole = await _db.UserRoles.SingleOrDefaultAsync(x => x.UserId == request.Id);
        if (oldRole != null && oldRole.RoleId == request.RoleId)
        {

        }
        else
        {
            if (oldRole != null && oldRole.RoleId != request.RoleId)
            {
                _db.UserRoles.Remove(oldRole);
            }
            await _db.UserRoles.AddAsync(new IdentityUserRole<string>
            {
                UserId = request.Id,
                RoleId = request.RoleId
            });
            await _db.SaveChangesAsync();
        }
        user.Name = request.Name;
        user.UserName = request.Username;
        user.Email = $"{request.Username}@gmail.com";
        var result = await _userManager.UpdateAsync(user);
        if (result.Succeeded)
        {
            return new BaseResponse
            {
                Success = true,
                Message = "Account update successful"
            };
        }
        else
        {
            var strError = "";
            var errors = result.Errors.ToList();
            foreach (var error in errors)
            {
                if (error.Code == "PasswordTooShort")
                {
                    strError += "Password must be at least 5 characters";
                }

                if (error.Code == "InvalidUserName")
                {
                    strError += "Invalid account name";
                }

                strError += ", ";
            }

            if (strError.EndsWith(", "))
            {
                strError = strError.Substring(0, strError.Length - 2);
            }
            return new BaseResponse
            {
                Success = false,
                Message = strError
            };
        }
    }

    public async Task<BaseResponse> DeleteAccountAsync(DeleteAccountRequest request)
    {
        var user = await _userManager.FindByIdAsync(request.UserId);
        if (user.UserName == request.CurrentUser)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Cannot delete currently logged in account"
            };
        }
        var result = await _userManager.DeleteAsync(user);
        if (result.Succeeded)
        {
            return new BaseResponse
            {
                Success = true,
                Message = "Account deleted successfully"
            };
        }
        else
        {
            var strError = "";
            var errors = result.Errors.ToList();
            foreach (var error in errors)
            {
                strError += error.Description;
                strError += ", ";
            }

            if (strError.EndsWith(", "))
            {
                strError = strError.Substring(0, strError.Length - 2);
            }
            return new BaseResponse
            {
                Success = false,
                Message = strError
            };
        }
    }

    public async Task<BaseResponse> ResetPasswordAsync(ResetPasswordRequest request)
    {
        var user = await _userManager.FindByIdAsync(request.UserId);
        if (user == null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "User does not exist"
            };
        }
        if (request.Password != request.ConfirmPassword)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Passwords do not match"
            };
        }
        var token = await _userManager.GeneratePasswordResetTokenAsync(user);
        var result = await _userManager.ResetPasswordAsync(user, token, request.Password);
        if (result.Succeeded)
        {
            return new BaseResponse
            {
                Success = true,
                Message = "Password reset successful"
            };
        }
        else
        {
            var strError = "";
            var errors = result.Errors.ToList();
            foreach (var error in errors)
            {
                if (error.Code == "PasswordTooShort")
                {
                    strError += "Password must be at least 5 characters";
                }

                strError += ", ";
            }

            if (strError.EndsWith(", "))
            {
                strError = strError.Substring(0, strError.Length - 2);
            }

            return new BaseResponse
            {
                Success = false,
                Message = strError
            };
        }
    }

    public async Task<BaseResponse> ChangePasswordAsync(ChangePasswordRequest request)
    {
        var user = await _userManager.FindByIdAsync(request.UserId);
        if (user == null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "User does not exist"
            };
        }

        if (request.NewPassword != request.ConfirmPassword)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Passwords do not match"
            };
        }

        var result = await _userManager.ChangePasswordAsync(user, request.OldPassword, request.NewPassword);
        if (result.Succeeded)
        {
            return new BaseResponse
            {
                Success = true,
                Message = "Password changed successfully"
            };
        }
        else
        {
            var strError = "";
            foreach (var error in result.Errors)
            {
                if (error.Code == "PasswordMismatch")
                {
                    strError += "Old password is incorrect";
                }

                if (error.Code == "PasswordTooShort")
                {
                    strError += "Password must be at least 5 characters";
                }

                strError += ", ";
            }
            if (strError.EndsWith(", "))
            {
                strError = strError.Substring(0, strError.Length - 2);
            }

            return new BaseResponse
            {
                Success = false,
                Message = strError
            };
        }
    }

    public async Task<BaseResponse> UpdatePermissionAsync(UpdatePermissionRequest request)
    {
        var oldRole = await _db.UserRoles.SingleOrDefaultAsync(x => x.UserId == request.UserId);
        if (oldRole != null && oldRole.RoleId == request.RoleId)
        {
            return new BaseResponse
            {
                Success = true,
                Message = "Role update successful"
            };
        }
        if (oldRole != null && oldRole.RoleId != request.RoleId)
        {
            _db.UserRoles.Remove(oldRole);
        }
        await _db.UserRoles.AddAsync(new IdentityUserRole<string>
        {
            UserId = request.UserId,
            RoleId = request.RoleId
        });
        await _db.SaveChangesAsync();
        return new BaseResponse
        {
            Success = true,
            Message = "Role update successful"
        };
    }
}