using AvrStream.Gateway.Api.Dto;
using AvrStream.Base.Streamer;
using AvrStream.Base.Streamer.Devices;
using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Models.Responses;

namespace AvrStream.Gateway.Api.Services;

public class DeviceService
{
    private readonly ILogger<DeviceService> _logger;
    private readonly IConfiguration _configuration;
    private readonly CameraSettingService _cameraSettingService;
    public AvrCam Cam1 { get; private set; }
    public AvrCam Cam2 { get; private set; }
    public AvrMic Mic1 { get; private set; }
    public AvrMic Mic2 { get; private set; }

    public DeviceService(ILogger<DeviceService> logger, IConfiguration configuration,
        CameraSettingService cameraSettingService)
    {
        _logger = logger;
        _configuration = configuration;
        _cameraSettingService = cameraSettingService;
    }

    public List<AvrDeviceDto> ListAvrDevicesAsync()
    {
        var list = StartupHostedService.DeviceMonitor.Devices;

        if (list == null) return null;

        var cams = list
            .Where(t => t.IsCam())
            .Select(t => t.ToAvrCam(_logger,
                _configuration.GetValue<int>("RecordSetting:CameraMinWidth"),
                _configuration.GetValue<int>("RecordSetting:CameraMinHeight"),
                _configuration.GetValue<int>("RecordSetting:CameraMaxSize"),
                _configuration.GetValue<bool>("RecordSetting:CameraOnly16By9"),
                _configuration.GetSection("RecordSetting:SupportedFps").Get<int[]>(),
                _configuration.GetValue<bool>("RecordSetting:CameraAllowSameBus"),
                _configuration.GetSection("RecordSetting:CamDisplayNamePrefixIncludes").Get<string[]>()))
            .Where(t => t != null)
            .OrderBy(t => t.DisplayName)
            .ThenBy(t => t.Bus)
            .ToList();
        Cam1 = cams.FirstOrDefault();
        Cam2 = cams.Skip(1).FirstOrDefault();

        var mics = list
            .Where(t => t.IsMic())
            .Select(t => t.ToAvrMic(_logger,
                _configuration.GetSection("RecordSetting:MicDisplayNamePrefixIncludes").Get<string[]>()))
            .Where(t => t != null)
            .OrderBy(t => t.Index)
            .ThenBy(t => t.Bus)
            .ToList();
        Mic1 = mics.FirstOrDefault();
        Mic2 = mics.Skip(1).FirstOrDefault();

        var devices = new List<AvrDeviceDto>();
        foreach (var cam in cams)
        {
            devices.Add(new AvrDeviceDto
            {
                Bus = cam.Bus,
                DisplayName = cam.DisplayName,
                Identifier = cam.Identifier,
                Index = cam.Index
            });
        }

        foreach (var mic in mics)
        {
            devices.Add(new AvrDeviceDto
            {
                Bus = mic.Bus,
                DisplayName = mic.DisplayName,
                Identifier = mic.Identifier,
                Index = mic.Index
            });
        }

        return devices;
    }


    public List<AvrCamDto> ListAvrCamerasAsync()
    {
        var list = StartupHostedService.DeviceMonitor.Devices;

        if (list == null) return null;

        var cams = list
            .Where(t => t.IsCam())
            .Select(t => t.ToAvrCam(_logger,
                _configuration.GetValue<int>("RecordSetting:CameraMinWidth"),
                _configuration.GetValue<int>("RecordSetting:CameraMinHeight"),
                _configuration.GetValue<int>("RecordSetting:CameraMaxSize"),
                _configuration.GetValue<bool>("RecordSetting:CameraOnly16By9"),
                _configuration.GetSection("RecordSetting:SupportedFps").Get<int[]>(),
                _configuration.GetValue<bool>("RecordSetting:CameraAllowSameBus"),
                _configuration.GetSection("RecordSetting:CamDisplayNamePrefixIncludes").Get<string[]>()))
            .Where(t => t != null)
            .OrderBy(t => t.DisplayName)
            .ThenBy(t => t.Bus)
            .ToList();
        var cameras = new List<AvrCamDto>();
        foreach (var cam in cams)
        {
            cameras.Add(new AvrCamDto
            {
                Index = cam.Index,
                Bus = cam.Bus,
                Focus = cam.Focus,
                Caps = cam.Caps,
                DisplayName = cam.DisplayName,
                Identifier = cam.Identifier
            });
        }

        return cameras;
    }

    public async Task<List<AvrCamSettingDto>> ListAvrCameraAndSettingAsync()
    {
        var list = StartupHostedService.DeviceMonitor.Devices;

        if (list == null) return null;

        var cams = list
            .Where(t => t.IsCam())
            .Select(t => t.ToAvrCam(_logger,
                _configuration.GetValue<int>("RecordSetting:CameraMinWidth"),
                _configuration.GetValue<int>("RecordSetting:CameraMinHeight"),
                _configuration.GetValue<int>("RecordSetting:CameraMaxSize"),
                _configuration.GetValue<bool>("RecordSetting:CameraOnly16By9"),
                _configuration.GetSection("RecordSetting:SupportedFps").Get<int[]>(),
                _configuration.GetValue<bool>("RecordSetting:CameraAllowSameBus"),
                _configuration.GetSection("RecordSetting:CamDisplayNamePrefixIncludes").Get<string[]>()))
            .Where(t => t != null)
            .OrderBy(t => t.DisplayName)
            .ThenBy(t => t.Bus)
            .ToList();
        var cameras = new List<AvrCamSettingDto>();
        foreach (var cam in cams)
        {
            var cameraRecordSetting =
                await _cameraSettingService.GetActiveCameraRecordSettingAsync(cam.DisplayName, cam.Bus);
            cameras.Add(new AvrCamSettingDto
            {
                Cam = new AvrCamDto
                {
                    Index = cam.Index,
                    Bus = cam.Bus,
                    Focus = cam.Focus,
                    Caps = cam.Caps,
                    DisplayName = cam.DisplayName,
                    Identifier = cam.Identifier
                },
                CameraRecordSetting = cameraRecordSetting
            });
        }

        return cameras;
    }

    public BaseResponse SetCameraFocus(CameraChangeFocusRequest request)
    {
        var cam = ListAvrCamerasAsync().FirstOrDefault(c => c.DisplayName == request.Name && c.Bus == request.Bus);
        if (cam == null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Camera not found"
            };
        }
        cam.SetFocus(request.IsAutoFocus ? 0 : request.FocusValue);
        return new BaseResponse
        {
            Success = true,
            Message = "Set focus success"
        };
    }
}