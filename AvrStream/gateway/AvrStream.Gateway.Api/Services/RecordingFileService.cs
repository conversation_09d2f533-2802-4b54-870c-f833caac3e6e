using AvrStream.Base.Models;
using AvrStream.Gateway.Api.Dto;
using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Models.Responses;
using AvrStream.Gateway.Entities.Databases;
using AvrStream.Gateway.Entities.Models;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Gateway.Api.Services;

public class RecordingFileService
{
    private readonly ILogger<RecordingFileService> _logger;
    private readonly IConfiguration _configuration;
    private readonly IServiceScopeFactory _scopeFactory;

    public RecordingFileService(IServiceScopeFactory scopeFactory, ILogger<RecordingFileService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        _scopeFactory = scopeFactory;
    }

    public async Task<ListResult<RecordingFileDto>> ListRecordingFilesAsync(ListRecordingFileRequest request)
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var url = _configuration.GetValue<string>("ApiUrl");
        var query = db.RecordingFiles.Where(f =>
            (request.FromDate == null || request.FromDate.Value.Date <= f.CreatedDate.Date) &&
            (request.EndDate == null || request.EndDate.Value.Date >= f.CreatedDate.Date));
        var total = await query.CountAsync();
        if (!request.IsTakeAll)
        {
            query = query.Skip((request.Page - 1) * request.Take).Take(request.Take);
        }

        var files = await query.Select(f => new RecordingFileDto
        {
            CreatedDate = f.CreatedDate,
            Id = f.Id,
            IsEncrypt = f.IsEncrypt,
            IsEndRecording = f.IsEndRecording,
            EncryptDone = f.EncryptDone,
            IsSynchronized = f.IsSynchronized,
            Password = f.Password,
            Iv = f.Iv,
            Path = f.Path,
            SynchronizedDate = f.SynchronizedDate,
            Name = f.Name,
            VideoUrl = $"{url}/api/video/{f.Id}"
        }).ToListAsync();
        return new ListResult<RecordingFileDto>(files, total);
    }

    public async Task<RecordingFileDto> GetRecordingFileAsync(Guid id)
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var recordingFile = await db.RecordingFiles.SingleOrDefaultAsync(f => f.Id == id);
        if (recordingFile == null)
        {
            return null;
        }

        return new RecordingFileDto
        {
            IsEndRecording = recordingFile.IsEndRecording,
            CreatedDate = recordingFile.CreatedDate,
            Id = recordingFile.Id,
            IsEncrypt = recordingFile.IsEncrypt,
            IsSynchronized = recordingFile.IsSynchronized,
            Password = recordingFile.Password,
            Path = recordingFile.Path,
            SynchronizedDate = recordingFile.SynchronizedDate
        };
    }

    public async Task<RecordingFileDto> GetRecordingFileAsync(string path)
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var recordingFile = await db.RecordingFiles.SingleOrDefaultAsync(f => f.Path == path);
        if (recordingFile == null)
        {
            return null;
        }

        return new RecordingFileDto
        {
            IsEndRecording = recordingFile.IsEndRecording,
            CreatedDate = recordingFile.CreatedDate,
            Id = recordingFile.Id,
            IsEncrypt = recordingFile.IsEncrypt,
            IsSynchronized = recordingFile.IsSynchronized,
            Password = recordingFile.Password,
            Path = recordingFile.Path,
            SynchronizedDate = recordingFile.SynchronizedDate
        };
    }

    public async Task<List<RecordingFile>> GetSyncRecordingFilesAsync()
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var recordingFiles = await db.RecordingFiles
            .Where(f => f.IsEncrypt && f.EncryptDone && f.IsEndRecording && !f.IsSynchronized ||
                        !f.IsEncrypt && !f.IsSynchronized)
            .ToListAsync();
        return recordingFiles;
    }

    public async Task<List<RecordingFile>> GetUnEncryptRecordingFilesAsync()
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var recordingFiles = await db.RecordingFiles
            .Where(f => f.IsEncrypt && !f.EncryptDone && f.IsEndRecording)
            .ToListAsync();
        return recordingFiles;
    }


    public BaseResponse CreateOrUpdateRecordingFile(CreateOrUpdateRecordingFileRequest request)
    {
        var scope = _scopeFactory.CreateScope();
        var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        if (request.Id == null)
        {
            var newRecordingFile = new RecordingFile
            {
                Name = request.Name,
                Path = request.Path,
                IsEncrypt = request.IsEncrypt,
                IsEndRecording = request.IsEndRecordingFile,
                IsSynchronized = request.IsSynchronized,
                Password = request.Password,
                SynchronizedDate = request.SynchronizedDate,
                Iv = request.Iv,
                EncryptDone = false
            };
            db.RecordingFiles.Add(newRecordingFile);
            db.SaveChangesAsync();
            return new BaseResponse
            {
                Success = true,
                Message = $"Create Recording File {request.Path} Successfully"
            };
        }
        else
        {
            var recordingFile = db.RecordingFiles.SingleOrDefault(f => f.Id == request.Id);
            if (recordingFile == null)
            {
                return new BaseResponse
                {
                    Success = false,
                    Message = $"Recording File {request.Id} does not exists"
                };
            }

            recordingFile.IsEncrypt = request.IsEncrypt;
            recordingFile.IsEndRecording = request.IsEndRecordingFile;
            recordingFile.EncryptDone = request.EncryptDone;
            recordingFile.IsSynchronized = request.IsSynchronized;
            recordingFile.Password = request.Password;
            recordingFile.Path = request.Path;
            recordingFile.SynchronizedDate = request.SynchronizedDate;
            recordingFile.Iv = request.Iv;
            db.Update(recordingFile);
            db.SaveChangesAsync();
            return new BaseResponse
            {
                Success = true,
                Message = $"Update Recording File {request.Path} Successfully"
            };
        }
    }

    // update field EncryptDone function
    public async Task<BaseResponse> UpdateRecordingFileAsync(RecordingFile recordingFile)
    {
        var scope = _scopeFactory.CreateScope();
        var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        db.Update(recordingFile);
        await db.SaveChangesAsync();
        return new BaseResponse
        {
            Success = true,
            Message = $"Update Encrypt Done {recordingFile.Id} Successfully"
        };
    }

    public async Task<BaseResponse> DeleteRecordingFileAsync(Guid id)
    {
        var scope = _scopeFactory.CreateScope();
        var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var recordingFile = await db.RecordingFiles.SingleOrDefaultAsync(f => f.Id == id);
        if (recordingFile == null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = $"Audio file {id} does not exist"
            };
        }

        if (File.Exists(recordingFile.Path))
        {
            var removeResult = RemoveFile(recordingFile.Path);
            if (!removeResult)
            {
                return new BaseResponse
                {
                    Success = false,
                    Message = "Delete recording file failed"
                };
            }
        }

        db.Remove(recordingFile);
        await db.SaveChangesAsync();
        return new BaseResponse
        {
            Success = true,
            Message = $"Deleted recording file {id} successfully"
        };
    }

    public async Task<BaseResponse> DeleteRecordingFileAsync(string path)
    {
        var scope = _scopeFactory.CreateScope();
        var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var recordingFile = await db.RecordingFiles.FirstOrDefaultAsync(f => f.Path == path);
        if (recordingFile == null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = $"Recording File with path {path} does not exists"
            };
        }

        if (File.Exists(recordingFile.Path))
        {
            var removeResult = RemoveFile(recordingFile.Path);
            if (!removeResult)
            {
                return new BaseResponse
                {
                    Success = false,
                    Message = "Delete file Error"
                };
            }
        }

        db.Remove(recordingFile);
        await db.SaveChangesAsync();
        return new BaseResponse
        {
            Success = true,
            Message = $"Delete Recording File  {path} Successfully"
        };
    }

    private bool RemoveFile(string path)
    {
        try
        {
            if (File.Exists(path))
            {
                File.Delete(path);
                return true;
            }
            else
            {
                _logger.LogError($"File {path} does not exists");
                return false;
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, null);
            return false;
        }
    }
}