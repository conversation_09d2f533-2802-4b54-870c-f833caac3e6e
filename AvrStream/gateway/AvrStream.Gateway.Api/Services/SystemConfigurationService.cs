using AvrStream.Gateway.Api.Dto;
using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Models.Responses;
using AvrStream.Gateway.Entities.Databases;
using AvrStream.Gateway.Entities.Models;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Gateway.Api.Services;

public class SystemConfigurationService
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly IConfiguration _configuration;

    public SystemConfigurationService(IServiceScopeFactory scopeFactory, IConfiguration configuration)
    {
        _configuration = configuration;
        _scopeFactory = scopeFactory;
    }

    public async Task<SystemSetting> GetSystemSettingAsync()
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var setting = await db.SystemSettings.SingleOrDefaultAsync();
        if (setting == null)
        {
            await CreateSystemSettingAsync();
            setting = await db.SystemSettings.SingleOrDefaultAsync();
        }

        return setting;
    }

    public async Task<SystemSettingDto> GetSystemSettingDtoAsync()
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var setting = await db.SystemSettings.SingleOrDefaultAsync();
        if (setting == null)
        {
            await CreateSystemSettingAsync();
            setting = await db.SystemSettings.SingleOrDefaultAsync();
        }

        return new SystemSettingDto
        {
            Name = setting.Name,
            IsEncrypted = setting.IsEncrypted,
            Password = setting.Password,
            Iv = setting.Iv,
            ServerUrl = setting.ServerUrl,
            SplitAfterMb = setting.SplitAfterMb,
            MaxFileLengthSeconds = setting.MaxFileLengthSeconds,
            RecordPath = setting.RecordPath,
            StreamUrl1 = setting.StreamUrl1,
            StreamUrl2 = setting.StreamUrl2,
            IsNoiseProcessing = setting.IsNoiseProcessing,
            NoiseProcessingLevel = setting.NoiseProcessingLevel,
            IsEchoProcessing = setting.IsEchoProcessing,
            EchoProcessingLevel = setting.EchoProcessingLevel,
        };
    }

    private async Task CreateSystemSettingAsync()
    {
        var scope = _scopeFactory.CreateScope();
        var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var exists = await db.SystemSettings.AnyAsync();
        if (!exists)
        {
            var isEncrypted = _configuration.GetValue<bool>("SystemSetting:IsEncrypted");
            var password = _configuration.GetValue<string>("SystemSetting:Password");
            var iv = _configuration.GetValue<string>("SystemSetting:Iv");
            var serverUrl = _configuration.GetValue<string>("SystemSetting:ServerUrl");
            var splitAfterMb = _configuration.GetValue<int>("SystemSetting:SplitAfterMb");
            var recordPath = _configuration.GetValue<string>("SystemSetting:RecordPath");
            var streamUrl1 = _configuration.GetValue<string>("SystemSetting:StreamUrl1");
            var streamUrl2 = _configuration.GetValue<string>("SystemSetting:StreamUrl2");
            var maxFileLengthSeconds = _configuration.GetValue<int>("SystemSetting:MaxFileLengthSeconds");
            var noiseProcessingLevel = _configuration.GetValue<int>("SystemSetting:NoiseProcessingLevel");
            var echoProcessingLevel = _configuration.GetValue<int>("SystemSetting:EchoProcessingLevel");
            var isNoiseProcessing = _configuration.GetValue<bool>("SystemSetting:IsNoiseProcessing");
            var isEchoProcessing = _configuration.GetValue<bool>("SystemSetting:IsEchoProcessing");
            var name = _configuration.GetValue<string>("SystemSetting:Name");
            var systemSetting = new SystemSetting
            {
                IsEncrypted = isEncrypted,
                Password = password,
                Iv = iv,
                ServerUrl = serverUrl,
                SplitAfterMb = splitAfterMb,
                RecordPath = recordPath,
                StreamUrl1 = streamUrl1,
                StreamUrl2 = streamUrl2,
                MaxFileLengthSeconds = maxFileLengthSeconds,
                IsNoiseProcessing = isNoiseProcessing,
                NoiseProcessingLevel = noiseProcessingLevel,
                IsEchoProcessing = isEchoProcessing,
                EchoProcessingLevel = echoProcessingLevel,
                Name = name
            };
            await db.AddAsync(systemSetting);
            await db.SaveChangesAsync();
        }
    }

    public async Task<BaseResponse> UpdateSystemSettingAsync(UpdateSystemSettingRequest request)
    {
        var scope = _scopeFactory.CreateScope();
        var db = scope.ServiceProvider.GetRequiredService<GatewayDbContext>();
        var systemSetting = await db.SystemSettings.SingleOrDefaultAsync();
        if (systemSetting == null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "SystemSetting does not exists"
            };
        }

        systemSetting.IsEncrypted = request.IsEncrypted;
        systemSetting.Password = request.Password;
        systemSetting.ServerUrl = request.ServerUrl;
        systemSetting.Name = request.Name;
        systemSetting.IsNoiseProcessing = request.IsNoiseProcessing;
        systemSetting.NoiseProcessingLevel = request.NoiseProcessingLevel;
        systemSetting.IsEchoProcessing = request.IsEchoProcessing;
        systemSetting.EchoProcessingLevel = request.EchoProcessingLevel;
        db.Update(systemSetting);
        await db.SaveChangesAsync();
        return new BaseResponse
        {
            Success = true,
            Message = "Cập nhật cài đặt thành công"
        };
    }
}