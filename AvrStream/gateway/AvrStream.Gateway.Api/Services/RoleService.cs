using AvrStream.Base.Models;
using AvrStream.Gateway.Api.Dto;
using AvrStream.Gateway.Api.Models;
using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Models.Responses;
using AvrStream.Gateway.Entities.Databases;
using AvrStream.Gateway.Entities.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using System.Security;

namespace AvrStream.Gateway.Api.Services;

public class RoleService
{
    private readonly UserDbContext _db;
    private readonly RoleManager<IdentityRole> _roleManager;
    private readonly UserManager<AppUser> _userManager;

    public RoleService(UserDbContext db, UserManager<AppUser> userManager, RoleManager<IdentityRole> roleManager)
    {
        _db = db;
        _roleManager = roleManager;
        _userManager = userManager;
    }

    public async Task<ListResult<RoleDto>> ListRolesAsync(ListRoleRequest request)
    {
        var query = _db.Roles.AsQueryable();
        var total = await query.CountAsync();
        if (!request.IsTakeAll)
        {
            query = query.Skip((request.Page - 1) * request.Take).Take(request.Take);
        }
        var roles = await query.Select(r => new RoleDto
        {
            Id = r.Id,
            Name = r.Name,
            Permission = new Permission()
        }).ToListAsync();
        var rolesClaims = await _db.RoleClaims.ToListAsync();
        foreach (var role in roles)
        {
            var claims = rolesClaims.Where(r => r.RoleId == role.Id).ToList();
            foreach (var claim in claims)
            {
                if (claim.ClaimType == "CreateAccount" && claim.ClaimValue == "True")
                {
                    role.Permission.CreateAccount = true;
                }
                else if (claim.ClaimType == "UpdateAccount" && claim.ClaimValue == "True")
                {
                    role.Permission.UpdateAccount = true;
                }
                else if (claim.ClaimType == "DeleteAccount" && claim.ClaimValue == "True")
                {
                    role.Permission.DeleteAccount = true;
                }
                else if (claim.ClaimType == "GetAccount" && claim.ClaimValue == "True")
                {
                    role.Permission.GetAccount = true;
                }
                else if (claim.ClaimType == "ResetPassword" && claim.ClaimValue == "True")
                {
                    role.Permission.ResetPassword = true;
                }
                else if (claim.ClaimType == "UpdatePermission" && claim.ClaimValue == "True")
                {
                    role.Permission.UpdatePermission = true;
                }
                else if (claim.ClaimType == "CreateRole" && claim.ClaimValue == "True")
                {
                    role.Permission.CreateRole = true;
                }
                else if (claim.ClaimType == "UpdateRole" && claim.ClaimValue == "True")
                {
                    role.Permission.UpdateRole = true;
                }
                else if (claim.ClaimType == "DeleteRole" && claim.ClaimValue == "True")
                {
                    role.Permission.DeleteRole = true;
                }
                else if (claim.ClaimType == "GetRole" && claim.ClaimValue == "True")
                {
                    role.Permission.GetRole = true;
                }
                else if (claim.ClaimType == "GetDevice" && claim.ClaimValue == "True")
                {
                    role.Permission.GetDevice = true;
                }
                else if (claim.ClaimType == "UpdateDevice" && claim.ClaimValue == "True")
                {
                    role.Permission.UpdateDevice = true;
                }
                else if (claim.ClaimType == "Preview" && claim.ClaimValue == "True")
                {
                    role.Permission.Preview = true;
                }
                else if (claim.ClaimType == "GetRecordingFile" && claim.ClaimValue == "True")
                {
                    role.Permission.GetRecordingFile = true;
                }
                else if (claim.ClaimType == "DeleteRecordingFile" && claim.ClaimValue == "True")
                {
                    role.Permission.DeleteRecordingFile = true;
                }
                else if (claim.ClaimType == "CameraSetting" && claim.ClaimValue == "True")
                {
                    role.Permission.CameraSetting = true;
                }
                else if (claim.ClaimType == "SystemSetting" && claim.ClaimValue == "True")
                {
                    role.Permission.SystemSetting = true;
                }
            }
        }
        return new ListResult<RoleDto>(roles, total);
    }

    public async Task<BaseResponse> CreateRoleAsync(CreateOrUpdateRoleRequest request)
    {
        var checkAtLeastPermission = CheckRoleAllFalse(request.Permission);
        if (checkAtLeastPermission)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Please select at least 1 permission"
            };
        }
        var existsRole = await _roleManager.FindByNameAsync(request.Name);
        if (existsRole != null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = $"A role with name {request.Name} already exists."
            };
        }

        var role = new IdentityRole
        {
            Name = request.Name
        };

        var created = await _roleManager.CreateAsync(role);
        if (created.Succeeded)
        {
            var result = await UpdateClaims(request.Permission, role);
            if (result)
            {
                return new BaseResponse
                {
                    Success = true,
                    Message = "Create successful roles"
                };
            }
            else
            {
                return new BaseResponse
                {
                    Success = false,
                    Message = "Create a failed role"
                };
            }
        }
        else
        {
            var strError = "";
            var errors = created.Errors.ToList();
            foreach (var error in errors)
            {
                strError += error.Description;
                strError += ", ";
            }

            if (strError.EndsWith(", "))
            {
                strError = strError.Substring(0, strError.Length - 2);
            }
            return new BaseResponse
            {
                Success = false,
                Message = strError
            };
        }
    }

    public async Task<BaseResponse> UpdateRoleAsync(CreateOrUpdateRoleRequest request)
    {
        var checkAtLeastPermission = CheckRoleAllFalse(request.Permission);
        if (checkAtLeastPermission)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Please select at least one permission"
            };
        }
        var role = await _roleManager.FindByIdAsync(request.Id);
        var existsRole = await _roleManager.FindByNameAsync(request.Name);
        if (existsRole != null && existsRole.Id != role.Id)
        {
            return new BaseResponse
            {
                Success = false,
                Message = $"Role {request.Name} already exists"
            };
        }
        role.Name = request.Name;
        await _roleManager.UpdateAsync(role);
        var result = await UpdateClaims(request.Permission, role);
        if (result)
        {
            return new BaseResponse
            {
                Success = true,
                Message = "Role update successful"
            };
        }
        else
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Role update failed"
            };
        }
    }

    public async Task<BaseResponse> DeleteRoleAsync(string roleId)
    {
        var existsUserRole = await _db.UserRoles.AnyAsync(x => x.RoleId == roleId);
        if (existsUserRole)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "The role cannot be deleted because it is assigned to an account."
            };
        }
        var role = await _roleManager.FindByIdAsync(roleId);
        var result = await _roleManager.DeleteAsync(role);
        if (result.Succeeded)
        {
            return new BaseResponse
            {
                Success = true,
                Message = "Delete role successfully"
            };
        }
        else
        {
            var strError = "";
            var errors = result.Errors.ToList();
            foreach (var error in errors)
            {
                strError += error.Description;
                strError += ", ";
            }

            if (strError.EndsWith(", "))
            {
                strError = strError.Substring(0, strError.Length - 2);
            }
            return new BaseResponse
            {
                Success = false,
                Message = strError
            };
        }
    }

    private bool CheckRoleAllFalse(Permission model)
    {
        if (model.CreateAccount || model.UpdateAccount || model.DeleteAccount ||
            model.GetAccount || model.ResetPassword || model.UpdatePermission ||
            model.CreateRole || model.UpdateRole || model.DeleteRole || model.GetRole || model.Preview ||
            model.GetDevice || model.UpdateDevice || model.GetRecordingFile || model.DeleteRecordingFile || model.CameraSetting || model.SystemSetting)
        {
            return false;
        }

        return true;
    }

    private async Task<bool> UpdateClaims(Permission model, IdentityRole role)
    {
        if ((model.CreateAccount || model.UpdateAccount || model.DeleteAccount ||
             model.UpdatePermission || model.ResetPassword) && !model.GetAccount)
        {
            model.GetAccount = true;
        }

        if ((model.CreateRole || model.UpdateRole || model.DeleteRole) && !model.GetRole)
        {
            model.GetRole = true;
        }

        if (model.UpdateDevice)
        {
            model.GetDevice = true;
        }

        if (model.DeleteRecordingFile)
        {
            model.GetRecordingFile = true;
        }

        //Account
        var allRoleClaims = await _db.RoleClaims.Where(x => x.RoleId == role.Id).ToListAsync();
        if (model.GetAccount)
        {
            var hasGetAccount = allRoleClaims.Any(x => x.ClaimType == "GetAccount" && x.ClaimValue == "True");
            if (!hasGetAccount)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "GetAccount",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "GetAccount" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }

        if (model.CreateAccount)
        {
            var hasCreateAccount = allRoleClaims.Any(x => x.ClaimType == "CreateAccount" && x.ClaimValue == "True");
            if (!hasCreateAccount)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "CreateAccount",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "CreateAccount" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }

        if (model.UpdateAccount)
        {
            var hasUpdateAccount = allRoleClaims.Any(x => x.ClaimType == "UpdateAccount" && x.ClaimValue == "True");
            if (!hasUpdateAccount)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "UpdateAccount",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "UpdateAccount" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }

        if (model.DeleteAccount)
        {
            var hasDeleteAccount = await _db.RoleClaims.AnyAsync(x => x.ClaimType == "DeleteAccount" && x.ClaimValue == "True");
            if (!hasDeleteAccount)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "DeleteAccount",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "DeleteAccount" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }

        if (model.ResetPassword)
        {
            var hasResetPassword = allRoleClaims.Any(x => x.ClaimType == "ResetPassword" && x.ClaimValue == "True");
            if (!hasResetPassword)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "ResetPassword",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "ResetPassword" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }

        if (model.UpdatePermission)
        {
            var hasUpdatePermission = allRoleClaims.Any(x => x.ClaimType == "UpdatePermission" && x.ClaimValue == "True");
            if (!hasUpdatePermission)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "UpdatePermission",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "UpdatePermission" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }


        //Role

        if (model.GetRole)
        {
            var hasGetRole = allRoleClaims.Any(x => x.ClaimType == "GetRole" && x.ClaimValue == "True");
            if (!hasGetRole)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "GetRole",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "GetRole" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }

        if (model.CreateRole)
        {
            var hasCreateRole = allRoleClaims.Any(x => x.ClaimType == "CreateRole" && x.ClaimValue == "True");
            if (!hasCreateRole)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "CreateRole",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "CreateRole" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }

        if (model.UpdateRole)
        {
            var hasUpdateRole = allRoleClaims.Any(x => x.ClaimType == "UpdateRole" && x.ClaimValue == "True");
            if (!hasUpdateRole)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "UpdateRole",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "UpdateRole" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }

        if (model.DeleteRole)
        {
            var hasDeleteRole = allRoleClaims.Any(x => x.ClaimType == "DeleteRole" && x.ClaimValue == "True");
            if (!hasDeleteRole)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "DeleteRole",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "DeleteRole" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }

        //Preview

        if (model.Preview)
        {
            var hasPreviewRole = allRoleClaims.Any(x => x.ClaimType == "Preview" && x.ClaimValue == "True");
            if (!hasPreviewRole)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "Preview",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "Preview" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }

        //Device

        if (model.GetDevice)
        {
            var hasGetDevice = allRoleClaims.Any(x => x.ClaimType == "GetDevice" && x.ClaimValue == "True");
            if (!hasGetDevice)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "GetDevice",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "GetDevice" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }


        if (model.UpdateDevice)
        {
            var hasUpdateDevice = allRoleClaims.Any(x => x.ClaimType == "UpdateDevice" && x.ClaimValue == "True");
            if (!hasUpdateDevice)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "UpdateDevice",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "UpdateDevice" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }

        //Recording File

        if (model.GetRecordingFile)
        {
            var hasRecordingFile = allRoleClaims.Any(x => x.ClaimType == "GetRecordingFile" && x.ClaimValue == "True");
            if (!hasRecordingFile)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "GetRecordingFile",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "GetRecordingFile" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }

        if (model.DeleteRecordingFile)
        {
            var hasDeleteRecordingFile = allRoleClaims.Any(x => x.ClaimType == "DeleteRecordingFile" && x.ClaimValue == "True");
            if (!hasDeleteRecordingFile)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "DeleteRecordingFile",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "DeleteRecordingFile" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }

        // Config

        if (model.CameraSetting)
        {
            var hasCameraSetting = allRoleClaims.Any(x => x.ClaimType == "CameraSetting" && x.ClaimValue == "True");
            if (!hasCameraSetting)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "CameraSetting",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "CameraSetting" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }

        if (model.SystemSetting)
        {
            var hasSystemSetting = allRoleClaims.Any(x => x.ClaimType == "SystemSetting" && x.ClaimValue == "True");
            if (!hasSystemSetting)
            {
                await _db.RoleClaims.AddAsync(new IdentityRoleClaim<string>
                {
                    RoleId = role.Id,
                    ClaimType = "SystemSetting",
                    ClaimValue = "True"
                });
            }
        }
        else
        {
            var roleClaims = allRoleClaims.Where(x => x.ClaimType == "SystemSetting" && x.ClaimValue == "True").ToList();
            if (roleClaims.Count > 0)
            {
                _db.RoleClaims.RemoveRange(roleClaims);
            }
        }


        var result = await _db.SaveChangesAsync();
        return result > 0;
    }
}