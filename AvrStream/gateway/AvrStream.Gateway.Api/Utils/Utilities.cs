using System.Net.NetworkInformation;
using System.Text.RegularExpressions;
using AvrStream.Gateway.Entities.Enums;
using AvrStream.Gateway.Streamer;

namespace AvrStream.Gateway.Api.Utils;

public static class Utilities
{
    private static string GetDataRootPath(String rootPath)
    {
        if (Path.IsPathRooted(rootPath)) return rootPath;
        return Path.GetFullPath(Path.Combine(AppContext.BaseDirectory, rootPath));
    }

    public static string GetDataPath(String rootPath)
    {
        return Path.Combine(GetDataRootPath(rootPath), "data");
    }

    public static string GetErrorPath(String rootPath)
    {
        return Path.Combine(rootPath, "error");
    }

    public static bool PingHost(string url)
    {
        Ping pingSender = new Ping();

        try
        {
            if (Uri.TryCreate(url, UriKind.Absolute, out var uri))
            {
                PingReply reply = pingSender.Send(uri.Host);
                if (reply == null) return false;
                return reply.Status == IPStatus.Success;
            }

            return false;
        }
        catch (PingException)
        {
            return false; // Return false if ping fails
        }
    }

    public static int? TryParse(string text)
    {
        // Use regular expression to find the numeric part after the underscore
        var match = Regex.Match(text, @"_(\d+)$");
        return match.Success && int.TryParse(match.Groups[1].Value, out int value) ? value : null;
    }

    public static string CreateRecordPath(String rootPath, string cameraName)
    {
        var path = Path.Combine(GetDataPath(rootPath), $"{DateTime.Now.Date:ddMMyyyy}",
            cameraName, "unencrypt");
        if (!Directory.Exists(path))
        {
            Directory.CreateDirectory(path);
        }

        var encryptFolderPath = Path.Combine(GetDataPath(rootPath),
            $"{DateTime.Now.Date:ddMMyyyy}",
            cameraName, "encrypt");

        if (!Directory.Exists(encryptFolderPath))
        {
            Directory.CreateDirectory(encryptFolderPath);
        }

        return path;
    }

    public static int GetMaxFileNumber(string sDir)
    {
        int highest =
            new DirectoryInfo(sDir)
                .GetFiles()
                .Select(x => Path.GetFileNameWithoutExtension(x.Name))
                .Select(TryParse)
                .Where(x => x.HasValue)
                .Select(x => x.Value)
                .DefaultIfEmpty(-1)
                .Max();
        return highest;
    }

    public static void MoveToError(string file)
    {
        try
        {
            // var errorPath = GetErrorPath(_sysSetting.RecordPath);
            var errorPath = "";
            var dir = Path.Combine(errorPath, Path.GetFileName(Path.GetDirectoryName(file))!);
            Directory.CreateDirectory(dir);
            File.Move(file, Path.Combine(dir, Path.GetFileName(file)), true);
        }
        catch (Exception)
        {
            //
        }
    }

    public static string GetValidFile(RecordSetting recordSetting)
    {
        var path = recordSetting.Path;
        if (!Directory.Exists(path))
            return null;
        // get path of the biggest number file and check it is a valid file
        var file = Directory.GetFiles(path)
            .Select(x => new { File = x, Number = TryParse(Path.GetFileNameWithoutExtension(x)) })
            .Where(x => x.Number.HasValue)
            .OrderByDescending(x => x.Number)
            .Select(x => x.File)
            .FirstOrDefault();

        return file;


        // var uri = new Uri(file);
        // var discover = new Discoverer(5_000_000_000);
        // try
        // {
        //     var info = discover.DiscoverUri(uri.AbsoluteUri);
        //     discover.Dispose();
        //     if (info.Duration > 0)
        //     {
        //         return file;
        //     }
        //     else
        //     {
        //         MoveToError(file);
        //         return null;
        //     }
        // }
        // catch (Exception e)
        // {
        //     return null;
        // }
    }

    public static int ConvertResolution(CameraResolution cameraResolution)
    {
        switch (cameraResolution)
        {
            case CameraResolution.UltraHd:
                return 3840;
            case CameraResolution.FullHd:
                return 1920;
            case CameraResolution.Hd:
                return 1280;
            default:
                return 0;
        }
    }
}