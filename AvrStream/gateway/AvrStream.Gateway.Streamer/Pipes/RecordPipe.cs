using System.Diagnostics;
using System.Runtime.InteropServices;
using AvrStream.Base.Streamer.Pipes;
using AvrStream.Gateway.Entities.Models;
using Gst;
using Microsoft.Extensions.Logging;
using DateTime = System.DateTime;
using MessageType = Gst.MessageType;
using Task = System.Threading.Tasks.Task;
using Uri = System.Uri;

namespace AvrStream.Gateway.Streamer.Pipes;

using Uri = Uri;

public class RecordPipe : BasePipe
{
    private static readonly Func<RecordSetting, string> CamFormat =
        (recordSetting) => @$"
{recordSetting.Cam.GetGstInput($"vid_src", true)} ! {recordSetting.Cap.GetGstCapInput()} ! videoflip video-direction={recordSetting.Direction} ! clockoverlay halignment=2 valignment=2 font-desc=""Verdana Medium 9"" time-format=""%d/%m/%Y %H:%M:%S"" !
{recordSetting.GstVideoEncodeElement} name=vid_enc bitrate={(RuntimeInformation.RuntimeIdentifier.Contains("ubuntu.18.04-arm64") ? recordSetting.CameraRecordSetting.BitRate : recordSetting.CameraRecordSetting.BitRate / 1000)} ! h264parse config-interval=-1 ! identity name=vid_identity sync=true ! tee name=cam_tee
cam_tee. ! queue leaky=downstream silent=true flush-on-eos=true ! identity sync=true ! muxer.video
cam_tee. ! queue leaky=downstream silent=true flush-on-eos=true ! identity sync=true ! s_local.";

    private static readonly Func<RecordSetting, SystemSetting, string> MicFormat =
        (recordSetting, systemSetting) =>
            @$"
{recordSetting.Mic.GetGstInput($"mic_src", true)} ! webrtcechoprobe name=webrtcechoprobe0 ! queue leaky=downstream silent=true flush-on-eos=true ! webrtcdsp name=webrtcdsp_mic
{(systemSetting.IsEchoProcessing ? $"echo-cancel=true echo-suppression-level={systemSetting.EchoProcessingLevel}" : "echo-cancel=false")} {(systemSetting.IsNoiseProcessing ? $"noise-suppression=true noise-suppression-level={systemSetting.NoiseProcessingLevel}" : "noise-suppression=false")} ! audioconvert ! audioresample ! level name=level message=true ! identity name=aud_identity sync=true ! tee name=mic_tee
mic_tee. ! queue leaky=downstream flush-on-eos=true ! {recordSetting.GstAudioEncodeElementMp4} ! identity sync=true ! queue leaky=downstream silent=true flush-on-eos=true ! muxer.audio_0
mic_tee. ! queue leaky=downstream flush-on-eos=true ! {recordSetting.GstAudioEncodeElementStream} ! identity sync=true ! queue leaky=downstream silent=true flush-on-eos=true ! s_local.";

    private static readonly Func<string, RecordSetting, SystemSetting, string> MuxFormat =
        (path, recordSetting, systemSetting) => @$"
    splitmuxsink name=muxer
    muxer=qtmux 
    location=""{path}/{DateTime.Now:HHmmss}_{recordSetting.RtspPart.Replace("/", "_")}_%05d.mp4"" 
    max-size-time={recordSetting.SplitAfterSeconds * GST_SECOND}
    max-size-bytes={systemSetting.SplitAfterMb * 1024L * 1024L}
    use-robust-muxing=true
    async-finalize=true
    start-index=0
    sink=filesink 
    muxer-properties=""properties,streamable=true,reserved-max-duration={recordSetting.SplitAfterSeconds * GST_SECOND + 60 * GST_SECOND},reserved-moov-update-period={5 * GST_SECOND}""
";

    private static readonly Func<RecordSetting, string> StreamFormat =
        (recordSetting) =>
            @$" rtspclientsink name=s_local location=rtsp://localhost:8554/{recordSetting.RtspPart}";

    private bool _stopping;
    private bool _eosRaise;
    private bool _forceStop;
    private System.Timers.Timer _timer;

    public RecordPipe(ILoggerFactory loggerFactory, SystemSetting systemSetting,
        RecordSetting recordSetting,
        Action<bool, bool> stateChangedHandler = null,
        Action<string> fileAddedHandler = null,
        Action eosHandler = null,
        Action<string> errorHandler = null) :
        base(loggerFactory)
    {
        // Initialize state flags
        _stopping = false;
        _eosRaise = false;
        _forceStop = false;

        var st = Stopwatch.StartNew();
        var desc = BuildPipelineDesc(systemSetting, recordSetting).Replace("\r\n", " ").Replace("\n", " ");
        Logger.LogInformation($"RECORD: gst-launch-1.0 {desc}");

        Instance = (Pipeline)Parse.Launch(desc);

        Instance.Bus.AddWatch((_, message) =>
        {
            switch (message.Type)
            {
                case MessageType.Element:
                    var structure = message.Structure;
                    if (structure == null) break;
                    var name = structure.Name;

                    if (string.Equals(name, "splitmuxsink-fragment-closed"))
                    {
                        var file = structure.GetString("location");

                        Logger.LogInformation($"New file added: {file}");
                        fileAddedHandler?.Invoke(file);

                        if (_stopping) Logger.LogInformation("Last file added, waiting EOS...");
                    }

                    break;
                case MessageType.StateChanged:
                    if (message.Src == Instance)
                    {
                        message.ParseStateChanged(out var oldState, out var newState, out var pending);

                        var isLoading = oldState == State.Null || oldState == State.Ready;
                        var isPlaying = pending == State.Playing ||
                                        (pending == State.VoidPending && newState == State.Playing);
                        var isChanging = pending != State.VoidPending;

                        stateChangedHandler?.Invoke(isPlaying, isChanging);

                        Logger.LogInformation(
                            $"STATE CHANGED: from {oldState.ToString()} to {newState.ToString()} (loading={isLoading}, pending={pending.ToString()})");
                    }

                    break;
                case MessageType.Error:
                    message.ParseError(out var err, out var debug);
                    Logger.LogError($"msg={err?.Message}, debug={debug}");
                    
                    _forceStop = true;

                    // Force immediate pipeline shutdown to prevent hanging
                    Task.Run(async () =>
                    {
                        await Task.Delay(1000); // Give a moment for cleanup
                        if (!_eosRaise && !_stopping)
                        {
                            try
                            {
                                Instance.SetState(State.Paused);
                                await Task.Delay(100);
                                Instance.SetState(State.Ready);
                                _eosRaise = true;
                            }
                            catch (Exception ex)
                            {
                                Logger.LogError(ex, "Error during force stop");
                                _eosRaise = true;
                            }
                        }
                    });

                    errorHandler?.Invoke($"{err?.Message}");
                    break;
                case MessageType.Eos:
                    Logger.LogInformation("Got EOS, disposing...");
                    _eosRaise = true;
                    eosHandler?.Invoke();
                    break;
            }

            return true;
        });
        Logger.LogInformation($"Setup record pipeline in {st.ElapsedMilliseconds}ms");

        st.Restart();
        Instance.SetState(State.Playing);
        Logger.LogInformation($"Set record pipeline to PLAYING in {st.ElapsedMilliseconds}ms");
    }

    private static string BuildPipelineDesc(SystemSetting systemSetting, RecordSetting recordSetting)
    {
        var videoDesc = "";
        if (recordSetting.Cam != null)
            videoDesc = CamFormat(recordSetting);

        var audioDesc = "";
        if (recordSetting.Mic != null)
            audioDesc = MicFormat(recordSetting, systemSetting);

        var path = new Uri(recordSetting.Path).AbsolutePath;
        var splitSinkDesc = MuxFormat(path, recordSetting, systemSetting);
        var streamDecs =
            StreamFormat(recordSetting);

        var desc = videoDesc + audioDesc + splitSinkDesc + streamDecs;
        return desc;
    }

    public void Stop()
    {
        _stopping = true;
        _eosRaise = false;
        var st = Stopwatch.StartNew();

        try
        {
            // If force stop is already triggered, skip EOS and go straight to force stop
            if (_forceStop)
            {
                Logger.LogWarning("Force stop already triggered, skipping EOS and forcing immediate stop");
                try
                {
                    Instance.SetState(State.Paused);
                    Thread.Sleep(100);
                    Instance.SetState(State.Ready);
                    _eosRaise = true;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error during immediate force stop");
                    _eosRaise = true;
                }
            }
            else
            {
                Logger.LogInformation("Stopping record pipeline by sending EOS...");
                Instance.SendEvent(Event.NewEos());

                _timer = new System.Timers.Timer(15000); // Reduced timeout for faster recovery
                _timer.Elapsed += (_, _) =>
                {
                    Logger.LogWarning("EOS not raised in 15s, force stopping pipeline...");
                    if (!_eosRaise)
                    {
                        try
                        {
                            Instance.SetState(State.Paused);
                            Thread.Sleep(50);
                            Instance.SetState(State.Ready);
                            _eosRaise = true;
                        }
                        catch (Exception ex)
                        {
                            Logger.LogError(ex, "Error during force stop");
                            _eosRaise = true; // Set to true to break the loop
                        }
                    }
                };

                _timer.Start();

                // Use more efficient waiting with reduced timeout
                var timeout = TimeSpan.FromSeconds(20); // Reduced from 35 to 20 seconds
                var startTime = DateTime.UtcNow;

                while (!_eosRaise && (DateTime.UtcNow - startTime) < timeout)
                {
                    Thread.Sleep(100);
                }

                if (!_eosRaise)
                {
                    Logger.LogError("Pipeline stop operation timed out completely, forcing final stop");
                    try
                    {
                        Instance.SetState(State.Null);
                        _eosRaise = true;
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, "Error during final force stop");
                        _eosRaise = true;
                    }
                }
            }
        }
        catch (Exception e)
        {
            Logger.LogError(e, "Error during pipeline stop operation");
        }
        finally
        {
            try
            {
                _timer?.Stop();
                _timer?.Dispose();
                _timer = null;

                Dispose();
                Logger.LogInformation($"Record pipeline stopped in {st.ElapsedMilliseconds}ms");
            }
            catch (Exception e)
            {
                Logger.LogError(e, "Error during pipeline cleanup");
            }
            finally
            {
                st.Stop();
            }
        }
    }

    public void UpdateBitrate(int newBitrate)
    {
        var videoEncoder = Instance.GetByName("vid_enc");
        if (videoEncoder != null)
        {
            // in jetson nano bitrate is in bps, else is in kbps
            videoEncoder.SetProperty("bitrate",
                RuntimeInformation.RuntimeIdentifier.Contains("ubuntu.18.04-arm64")
                    ? new GLib.Value(newBitrate)
                    : new GLib.Value(newBitrate / 1000));

            Logger.LogInformation($"Updated bitrate to {newBitrate}");
        }
        else
        {
            Logger.LogWarning("Video encoder element not found.");
        }
    }

    public void UpdateNoiseProcessing(bool isNoiseProcessing, int noiseProcessingLevel)
    {
        var micProcessing = Instance.GetByName("webrtcdsp_mic");
        if (micProcessing != null)
        {
            if (isNoiseProcessing)
            {
                micProcessing.SetProperty("noise-suppression", new GLib.Value(true));
                micProcessing.SetProperty("noise-suppression-level", new GLib.Value(noiseProcessingLevel));
            }
            else
            {
                micProcessing.SetProperty("noise-suppression", new GLib.Value(false));
            }

            Logger.LogInformation($"Updated noise processing to {isNoiseProcessing} with level {noiseProcessingLevel}");
        }
        else
        {
            Logger.LogWarning("Mic webrtcdsp element not found.");
        }
    }

    public void UpdateEchoProcessing(bool isEchoProcessing, int echoProcessingLevel)
    {
        var micProcessing = Instance.GetByName("webrtcdsp_mic");
        if (micProcessing != null)
        {
            if (isEchoProcessing)
            {
                // temporary disable echo processing because of error: g_object_ref: assertion 'G_IS_OBJECT (object)' failed
                // micProcessing.SetProperty("echo-cancel", new GLib.Value(true));
                micProcessing.SetProperty("echo-suppression-level", new GLib.Value(echoProcessingLevel));
            }
            else
            {
                micProcessing.SetProperty("echo-cancel", new GLib.Value(false));
            }

            Logger.LogInformation(
                $"Updated echo processing to {isEchoProcessing} with level {echoProcessingLevel}");
        }
        else
        {
            Logger.LogWarning("Mic webrtcdsp element not found.");
        }
    }

    /// <summary>
    /// Reset pipeline state flags for restart scenarios
    /// </summary>
    public void ResetState()
    {
        _stopping = false;
        _eosRaise = false;
        _forceStop = false;
        Logger.LogInformation("Pipeline state flags reset");
    }

    /// <summary>
    /// Check if pipeline is in a healthy state
    /// </summary>
    public bool IsHealthy()
    {
        try
        {
            if (Instance == null) return false;

            var state = Instance.CurrentState;
            var pendingState = Instance.PendingState;

            // Pipeline is healthy if it's playing or transitioning to playing
            var isHealthy = (state == State.Playing || pendingState == State.Playing) && !_forceStop;

            Logger.LogDebug(
                $"Pipeline health check: State={state}, Pending={pendingState}, ForceStop={_forceStop}, Healthy={isHealthy}");
            return isHealthy;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error checking pipeline health");
            return false;
        }
    }
}