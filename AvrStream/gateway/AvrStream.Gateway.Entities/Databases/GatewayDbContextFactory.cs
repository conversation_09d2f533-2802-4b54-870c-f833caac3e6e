using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace AvrStream.Gateway.Entities.Databases;

public class GatewayDbContextFactory : IDesignTimeDbContextFactory<GatewayDbContext>
{
    public GatewayDbContext CreateDbContext(string[] args)
    {
        var builder = new DbContextOptionsBuilder<GatewayDbContext>();
        builder.UseNpgsql("Server=localhost;Port=5432;Database=Gateway_dev;Username=postgres;Password=yourStrong(!!)Pa$$word;");
        return new GatewayDbContext(builder.Options);
    }
}