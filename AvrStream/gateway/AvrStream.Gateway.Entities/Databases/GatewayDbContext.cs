using AvrStream.Gateway.Entities.Configurations;
using AvrStream.Gateway.Entities.Models;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Gateway.Entities.Databases;

public sealed class GatewayDbContext : DbContext
{
    public GatewayDbContext(DbContextOptions<GatewayDbContext> options) : base(options)
    {
        AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
        Database.Migrate();
    }
    public DbSet<RecordingFile> RecordingFiles { get; set; }
    public DbSet<SystemSetting> SystemSettings { get; set; }
    public DbSet<CameraRecordSetting> CameraRecordSettings { get; set; }
    public DbSet<BoxMessage> Messages { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        modelBuilder.ApplyConfiguration(new RecordingFileConfiguration());
        modelBuilder.ApplyConfiguration(new SystemSettingConfiguration());
        modelBuilder.ApplyConfiguration(new CameraRecordSettingConfiguration());
        modelBuilder.ApplyConfiguration(new BoxMessageConfiguration());
    }

    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        foreach (var entry in ChangeTracker.Entries<BaseEntity>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedDate = DateTime.Now;
                    break;
                case EntityState.Modified:
                    entry.Entity.LastUpdatedDate = DateTime.Now;
                    break;
            }
        }
        return base.SaveChangesAsync(cancellationToken);
    }
}