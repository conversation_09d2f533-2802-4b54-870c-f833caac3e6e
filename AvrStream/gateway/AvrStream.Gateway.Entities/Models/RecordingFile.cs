namespace AvrStream.Gateway.Entities.Models;

public class RecordingFile : BaseEntity
{
    public string Name { get; set; }
    public string Password { get; set; }
    public string Iv { get; set; }
    public bool IsEncrypt { get; set; }
    public bool EncryptDone { get; set; }
    public string Path { get; set; }
    public bool IsEndRecording { get; set; }
    public bool IsSynchronized { get; set; }
    public DateTime? SynchronizedDate { get; set; }
    public string Hash { get; set; }
    public Guid? RecordingFileIdServer { get; set; }
}