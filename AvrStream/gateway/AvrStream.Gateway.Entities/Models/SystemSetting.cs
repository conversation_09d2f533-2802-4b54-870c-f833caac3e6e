namespace AvrStream.Gateway.Entities.Models;

public class SystemSetting : BaseEntity
{
    public string Name { get; set; }
    public string Password { get; set; }
    public string Iv { get; set; }
    public bool IsEncrypted { get; set; }
    public int SplitAfterMb { get; set; }
    public string ServerUrl { get; set; }
    public string RecordPath { get; set; }
    public int MaxFileLengthSeconds { get; set; }
    public string StreamUrl1 { get; set; }
    public string StreamUrl2 { get; set; }
    public bool IsNoiseProcessing { get; set; }
    public int NoiseProcessingLevel { get; set; }
    public bool IsEchoProcessing { get; set; }
    public int EchoProcessingLevel { get; set; }
}