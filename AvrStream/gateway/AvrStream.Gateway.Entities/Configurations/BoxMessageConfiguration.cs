using AvrStream.Gateway.Entities.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AvrStream.Gateway.Entities.Configurations;

public class BoxMessageConfiguration : IEntityTypeConfiguration<BoxMessage>
{
    public void Configure(EntityTypeBuilder<BoxMessage> builder)
    {
        builder.ToTable("BoxMessages");
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).ValueGeneratedOnAdd();
        builder.Property(x => x.Message).HasMaxLength(5000);
    }
}