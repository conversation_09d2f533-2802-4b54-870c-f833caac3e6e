using AvrStream.Gateway.Entities.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AvrStream.Gateway.Entities.Configurations;

public class SystemSettingConfiguration : IEntityTypeConfiguration<SystemSetting>
{
    public void Configure(EntityTypeBuilder<SystemSetting> builder)
    {
        builder.ToTable("SystemSettings");
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).ValueGeneratedOnAdd();
        builder.Property(x => x.Password).HasMaxLength(500);
        builder.Property(x => x.ServerUrl).HasMaxLength(100);
        builder.Property(x => x.RecordPath).HasMaxLength(500);
        builder.Property(x => x.StreamUrl1).HasMaxLength(100);
        builder.Property(x => x.StreamUrl2).HasMaxLength(100);
        builder.Property(x => x.Name).HasMaxLength(500);
        builder.Property(x => x.Iv).HasMaxLength(500);
    }
}