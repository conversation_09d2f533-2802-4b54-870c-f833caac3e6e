using AvrStream.Gateway.Entities.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AvrStream.Gateway.Entities.Configurations;

public class CameraRecordSettingConfiguration : IEntityTypeConfiguration<CameraRecordSetting>
{
    public void Configure(EntityTypeBuilder<CameraRecordSetting> builder)
    {
        builder.ToTable("CameraRecordingSettings");
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).ValueGeneratedOnAdd();
        builder.Property(x => x.Name).HasMaxLength(500);
        builder.Property(x => x.Bus).HasMaxLength(500);
    }
}