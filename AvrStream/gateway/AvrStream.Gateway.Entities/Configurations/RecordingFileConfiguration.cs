using AvrStream.Gateway.Entities.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AvrStream.Gateway.Entities.Configurations;

public class RecordingFileConfiguration : IEntityTypeConfiguration<RecordingFile>
{
    public void Configure(EntityTypeBuilder<RecordingFile> builder)
    {
        builder.ToTable("RecordingFiles");
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).ValueGeneratedOnAdd();
        builder.Property(x => x.Password).HasMaxLength(500);
        builder.Property(x => x.Path).HasMaxLength(500);
        builder.Property(x => x.Name).HasMaxLength(500);
        builder.Property(x => x.Hash).HasMaxLength(5000);
        builder.Property(x => x.Iv).HasMaxLength(500);
        builder.HasIndex(x => new { x.Id, x.Path, x.Name })
            .HasMethod("GIN")
            .IsTsVectorExpressionIndex("english");
    }
}