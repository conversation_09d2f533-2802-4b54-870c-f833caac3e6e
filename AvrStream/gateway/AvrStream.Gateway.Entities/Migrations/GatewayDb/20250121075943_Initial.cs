using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AvrStream.Gateway.Entities.Migrations.GatewayDb
{
    public partial class Initial : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "BoxMessages",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    MessageType = table.Column<int>(type: "integer", nullable: false),
                    Message = table.Column<string>(type: "character varying(5000)", maxLength: 5000, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BoxMessages", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CameraRecordingSettings",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Bus = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    FocusValue = table.Column<int>(type: "integer", nullable: false),
                    IsAutofocus = table.Column<bool>(type: "boolean", nullable: false),
                    Resolution = table.Column<int>(type: "integer", nullable: false),
                    Fps = table.Column<int>(type: "integer", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    BitRate = table.Column<int>(type: "integer", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CameraRecordingSettings", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "RecordingFiles",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Password = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Iv = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsEncrypt = table.Column<bool>(type: "boolean", nullable: false),
                    EncryptDone = table.Column<bool>(type: "boolean", nullable: false),
                    Path = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsEndRecording = table.Column<bool>(type: "boolean", nullable: false),
                    IsSynchronized = table.Column<bool>(type: "boolean", nullable: false),
                    SynchronizedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    Hash = table.Column<string>(type: "character varying(5000)", maxLength: 5000, nullable: true),
                    RecordingFileIdServer = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RecordingFiles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SystemSettings",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Password = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Iv = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsEncrypted = table.Column<bool>(type: "boolean", nullable: false),
                    SplitAfterMb = table.Column<int>(type: "integer", nullable: false),
                    ServerUrl = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    RecordPath = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    MaxFileLengthSeconds = table.Column<int>(type: "integer", nullable: false),
                    StreamUrl1 = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    StreamUrl2 = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    IsNoiseProcessing = table.Column<bool>(type: "boolean", nullable: false),
                    NoiseProcessingLevel = table.Column<int>(type: "integer", nullable: false),
                    IsEchoProcessing = table.Column<bool>(type: "boolean", nullable: false),
                    EchoProcessingLevel = table.Column<int>(type: "integer", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SystemSettings", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_RecordingFiles_Id_Path_Name",
                table: "RecordingFiles",
                columns: new[] { "Id", "Path", "Name" })
                .Annotation("Npgsql:IndexMethod", "GIN")
                .Annotation("Npgsql:TsVectorConfig", "english");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BoxMessages");

            migrationBuilder.DropTable(
                name: "CameraRecordingSettings");

            migrationBuilder.DropTable(
                name: "RecordingFiles");

            migrationBuilder.DropTable(
                name: "SystemSettings");
        }
    }
}
