// <auto-generated />
using System;
using AvrStream.Gateway.Entities.Databases;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace AvrStream.Gateway.Entities.Migrations.GatewayDb
{
    [DbContext(typeof(GatewayDbContext))]
    [Migration("20250121075943_Initial")]
    partial class Initial
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.35")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("AvrStream.Gateway.Entities.Models.BoxMessage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("LastUpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Message")
                        .HasMaxLength(5000)
                        .HasColumnType("character varying(5000)");

                    b.Property<int>("MessageType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("BoxMessages", (string)null);
                });

            modelBuilder.Entity("AvrStream.Gateway.Entities.Models.CameraRecordSetting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("BitRate")
                        .HasColumnType("integer");

                    b.Property<string>("Bus")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("FocusValue")
                        .HasColumnType("integer");

                    b.Property<int>("Fps")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAutofocus")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastUpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Name")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("Resolution")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("CameraRecordingSettings", (string)null);
                });

            modelBuilder.Entity("AvrStream.Gateway.Entities.Models.RecordingFile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<bool>("EncryptDone")
                        .HasColumnType("boolean");

                    b.Property<string>("Hash")
                        .HasMaxLength(5000)
                        .HasColumnType("character varying(5000)");

                    b.Property<bool>("IsEncrypt")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEndRecording")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSynchronized")
                        .HasColumnType("boolean");

                    b.Property<string>("Iv")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("LastUpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Name")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Password")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Path")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid?>("RecordingFileIdServer")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("SynchronizedDate")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("Id");

                    b.HasIndex("Id", "Path", "Name")
                        .HasAnnotation("Npgsql:TsVectorConfig", "english");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex("Id", "Path", "Name"), "GIN");

                    b.ToTable("RecordingFiles", (string)null);
                });

            modelBuilder.Entity("AvrStream.Gateway.Entities.Models.SystemSetting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("EchoProcessingLevel")
                        .HasColumnType("integer");

                    b.Property<bool>("IsEchoProcessing")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEncrypted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsNoiseProcessing")
                        .HasColumnType("boolean");

                    b.Property<string>("Iv")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("LastUpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("MaxFileLengthSeconds")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("NoiseProcessingLevel")
                        .HasColumnType("integer");

                    b.Property<string>("Password")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("RecordPath")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ServerUrl")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("SplitAfterMb")
                        .HasColumnType("integer");

                    b.Property<string>("StreamUrl1")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("StreamUrl2")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.ToTable("SystemSettings", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
