using AvrStream.Gateway.Entities.Databases;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace AvrStream.Gateway.Entities;

public static class EntitiesExtensions
{
    public static IServiceCollection AddGatewayEntities(this IServiceCollection serviceCollection, string cnn)
    {
        serviceCollection
            .AddDbContext<GatewayDbContext>(b =>
                b.UseNpgsql(cnn, opts => opts.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery)), ServiceLifetime.Transient)
            ;

        return serviceCollection;
    }

    public static IServiceCollection AddUserEntities(this IServiceCollection serviceCollection, string cnn)
    {
        serviceCollection
            .AddDbContext<UserDbContext>(b =>
                b.UseNpgsql(cnn, opts => opts.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery)))
            ;

        return serviceCollection;
    }
}