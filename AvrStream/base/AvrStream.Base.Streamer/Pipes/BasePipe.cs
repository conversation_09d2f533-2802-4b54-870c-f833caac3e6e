using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using Gst;
using Microsoft.Extensions.Logging;
using Timer = System.Timers.Timer;
using Value = GLib.Value;
using ValueArray = GLib.ValueArray;

namespace AvrStream.Base.Streamer.Pipes;

public abstract class BasePipe : IDisposable
{
    protected const long GST_SECOND = 1000_000_000;
    protected readonly ILogger Logger;

    private bool _disposed;

    /* This field will be created in ctor of in derived classes */
    protected Pipeline Instance;

    protected BasePipe(ILoggerFactory factory)
    {
        Logger = factory.CreateLogger(GetType());
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        try
        {
            var st = Stopwatch.StartNew();
            Logger.LogInformation("Pipeline disposing...");
            Instance.SetState(State.Null);
            Instance.Dispose();
            Instance = null!;
            Logger.LogInformation($"Pipeline disposed in {st.ElapsedMilliseconds}ms");
        }
        catch (Exception e)
        {
            Logger.LogError(e, nameof(Dispose));
            // TODO: check reason for exception
            throw;
        }
    }

    /// <summary>
    ///     Call native method via reflection (re-use binding of GLibSharp/GstSharp...)
    /// </summary>
    [SuppressMessage("ReSharper", "InconsistentNaming")]
    private static class NativeWrapper
    {
        private static readonly object _field_g_value_get_boxed = typeof(Value)
            .GetField("g_value_get_boxed", BindingFlags.Static | BindingFlags.NonPublic)!
            .GetValue(null)!;

        private static readonly MethodInfo _method_g_value_get_boxed =
            _field_g_value_get_boxed.GetType().GetMethod("Invoke")!;

        private static readonly object _field_g_value_array_copy = typeof(ValueArray)
            .GetField("g_value_array_copy", BindingFlags.Static | BindingFlags.NonPublic)!
            .GetValue(null)!;

        private static readonly MethodInfo _method_g_value_array_copy =
            _field_g_value_array_copy.GetType().GetMethod("Invoke")!;

        public static IntPtr g_value_get_boxed(Value value)
        {
            return (IntPtr)_method_g_value_get_boxed.Invoke(_field_g_value_get_boxed, new object[] { value })!;
        }

        public static IntPtr g_value_array_copy(IntPtr raw)
        {
            return (IntPtr)_method_g_value_array_copy.Invoke(_field_g_value_array_copy, new object[] { raw })!;
        }
    }
}