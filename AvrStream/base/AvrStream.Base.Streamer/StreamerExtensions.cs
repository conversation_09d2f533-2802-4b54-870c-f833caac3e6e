using AvrStream.Base.Streamer.Devices;
using Gst;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;

namespace AvrStream.Base.Streamer;

public static class StreamerExtensions
{
    public static bool IsCam(this Device dev)
    {
        return dev.DeviceClass == "Video/Source" || dev.DeviceClass == "Source/Video";
    }

    public static bool IsMic(this Device dev)
    {
        return dev.DeviceClass == "Audio/Source" || dev.DeviceClass == "Source/Audio";
    }

    private static int ExtractValue(string input, string fieldName)
    {
        string pattern = fieldName + @"=(\d+)"; // Regex pattern to match the field and capture the value

        Match match = Regex.Match(input, pattern);

        if (match.Success)
        {
            string valueString = match.Groups[1].Value;
            if (int.TryParse(valueString, out var value))
            {
                return value;
            }
        }

        return -1; // Return a default value if the field is not found or the extraction fails
    }

    public static AvrCam? ToAvrCam(this Device device, ILogger logger, int minWidth, int minHeight, int maxSize,
        bool only16By9,
        int[] supportedFps,
        bool allowSameBus,
        string[] displayNamePrefixIncludes)
    {
        var converted = false;
        logger.LogDebug(
            $"Converting device to Camera: {device.DisplayName} ({device.Name}), props: {device.Properties?.ToString()}");

        try
        {
            /* filter supported API per OS */
            var displayName = device.DisplayName;
            string? deviceIdentifier;
            int deviceIndex;
            string? devicePath;

            if (!displayNamePrefixIncludes.Any(t =>
                    !string.IsNullOrEmpty(t) && displayName.Contains(t, StringComparison.InvariantCultureIgnoreCase)))
                return null;

            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                if (device.Properties?.GetString("device.api") != "v4l2") return null;

                deviceIdentifier = device.Properties?.GetString("api.v4l2.path") ??
                                   device.Properties?.GetString("device.path");
                if (string.IsNullOrEmpty(deviceIdentifier)) return null;

                if (!int.TryParse(deviceIdentifier.Replace("/dev/video", ""), out deviceIndex)) return null;
                devicePath = device.Properties?.GetString("api.v4l2.cap.bus_info") ??
                             device.Properties?.GetString("v4l2.device.bus_info");

                if (string.IsNullOrEmpty(devicePath)) return null;
            }
            else
            {
                throw new NotSupportedException();
            }

            var deviceCaps = device.Caps;
            if (deviceCaps == null) return null;

            var caps = new List<AvrVideoCap>();
            var focus = new AvrCamFocus();
            foreach (Structure cap in deviceCaps)
                if (
                    cap.GetInt("width", out var width) && width >= minWidth &&
                    cap.GetInt("height", out var height) && height >= minHeight &&
                    (!only16By9 || width * 9 / 16 == height) &&
                    (maxSize <= 0 || width * height <= maxSize)
                )
                {
                    /* check name */
                    bool isImage;
                    var name = cap.Name;
                    if (name == AvrVideoCap.NameImageJpeg)
                        isImage = true;
                    else
                        continue;
                    // Only support image/jpeg format 
                    // if (name != AvrVideoCap.NameVideoXRaw) continue;

                    string? format = null;
                    if (!isImage)
                    {
                        // only get format when is video
                        format = cap.GetString("format");
                        if (format == null)
                        {
                            /* try get format from list */
                            logger.LogWarning($"TODO: parse format for cap: {cap}");
                            continue;
                        }
                    }

                    /* FPS:
                    - if cap has single fps => parse to list supported fps
                    - if cap has list fps => parse list to list, trim duplicate
                    - if cap has range fps => get supported fps in the range
                 */

                    if (cap.GetFraction("framerate", out var numerator, out var denominator) && denominator > 0)
                    {
                        var nearestFps = numerator / denominator;
                        caps.AddRange(supportedFps.Where(t => t == nearestFps).Select(t => new AvrVideoCap
                        {
                            Width = width,
                            Height = height,
                            FpsNumerator = numerator,
                            FpsDenominator = denominator,
                            Fps = t,
                            IsImage = isImage,
                            Format = format
                        }));
                    }
                    else
                    {
                        var fpsValue = cap.GetValue("framerate");
                        if (fpsValue.Val is FractionRange range)
                        {
                            var minFps = range.Min.Numerator / range.Min.Denominator;
                            var maxFps = range.Max.Numerator / range.Max.Denominator;

                            // with fps in range [minFps, maxFps] device support any fps
                            foreach (var fps in supportedFps.Where(t => t >= minFps && t <= maxFps))
                                caps.Add(new AvrVideoCap
                                {
                                    Width = width,
                                    Height = height,
                                    FpsNumerator = fps,
                                    FpsDenominator = 1,
                                    Fps = fps,
                                    IsImage = isImage,
                                    Format = format
                                });
                        }
                        else if (fpsValue.Val is List list)
                        {
                            foreach (Fraction val in list)
                            {
                                // add each fps
                                var nearestFps = val.Numerator / val.Denominator;
                                caps.AddRange(supportedFps.Where(t => t <= nearestFps).Select(t => new AvrVideoCap
                                {
                                    Width = width,
                                    Height = height,
                                    FpsNumerator = val.Numerator,
                                    FpsDenominator = val.Denominator,
                                    Fps = t,
                                    IsImage = isImage,
                                    Format = format
                                }));
                            }
                        }
                        else
                        {
                            logger.LogWarning($"TODO: framerate is list or array: {cap}");
                        }
                    }
                }

            // filter derive caps that exists as native support
            caps = caps.GroupBy(t => new { t.Width, t.Height, t.Fps, t.IsImage })
                .Select(k => k.FirstOrDefault(x => x.IsNative()) ?? k.First())
                .OrderByDescending(t => t.Width * t.Height * t.Fps)
                .ToList();

            if (caps.Count == 0) return null;

            converted = true;

            var info = new ProcessStartInfo("v4l2-ctl", $"-l -d {deviceIdentifier}")
            {
                RedirectStandardOutput = true
            };

            var process = Process.Start(info);
            if (false == process!.WaitForExit(1000)) // Wait for 1 seconds
            {
                process.Kill();
            }

            string rawData = process.StandardOutput.ReadToEnd();
            var focusData = Regex.Matches(rawData, @"focus_absolute.*\n")
                .Select(m => m.Value)
                .FirstOrDefault()
                ?.Trim().Split(":");
            var focusAutomaticContinuousData = Regex.Matches(rawData, @"focus_auto.*\n")
                .Select(m => m.Value)
                .FirstOrDefault()
                ?.Trim().Split(":");
            if (focusData != null && focusAutomaticContinuousData != null)
            {
                var minFocus = ExtractValue(focusData[1], "min");
                var maxFocus = ExtractValue(focusData[1], "max");
                var valueFocus = ExtractValue(focusData[1], "value");
                var isFocus = ExtractValue(focusAutomaticContinuousData[1], "value");
                focus = new AvrCamFocus
                {
                    Identifier = deviceIdentifier,
                    Min = minFocus,
                    Max = maxFocus,
                    Value = valueFocus,
                    Focus = isFocus
                };
                logger.LogInformation(
                    $"Camera {displayName} focus data: focus: {focus.Focus}, value focus: {focus.Value}, mix value focus: {focus.Min}, max value focus: {focus.Max}");
            }

            return new AvrCam(caps, focus)
            {
                DisplayName = displayName,
                Identifier = deviceIdentifier,
                Index = deviceIndex,
                Bus = devicePath
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "ToAvrCam()->ProcessStartInfo(\"v4l2-ctl\")");
            return null;
        }
        finally
        {
            logger.LogDebug(converted
                ? $"Device {device.DisplayName} ({device.Name}) has been converted to AvrCam"
                : $"Device {device.DisplayName} ({device.Name}) is invalid AvrCam");
        }
    }

    public static AvrMic? ToAvrMic(this Device device, ILogger logger, string[] displayNamePrefixIncludes)
    {
        var converted = false;
        logger.LogDebug(
            $"Converting device to micro: {device.DisplayName} ({device.Name}), props: {device.Properties?.ToString()}");
        try
        {
            /* filter supported API per OS */
            var displayName = device.DisplayName ?? "";

            if (!displayNamePrefixIncludes.Any(t =>
                    !string.IsNullOrEmpty(t) && displayName.Contains(t, StringComparison.InvariantCultureIgnoreCase)))
                return null;

            string? deviceIdentifier;
            var deviceIndex = 0;
            string? deviceBus;
            var useAlsaSrc = false;

            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                if (device.Properties?.GetString("device.api") != "alsa") return null;

                /* get pulse src identifier */
                var udevId = device.Properties?.GetString("udev.id");
                var profileName = device.Properties?.GetString("device.profile.name");
                var cardName = device.Properties?.GetString("alsa.card_name");
                if (!string.IsNullOrEmpty(cardName) && displayName.Length > cardName.Length)
                    displayName = cardName;

                if (!string.IsNullOrEmpty(udevId) || string.IsNullOrEmpty(profileName))
                {
                    int alsaCardInt = -1;
                    device.Properties?.GetInt("alsa.card", out alsaCardInt);

                    if (alsaCardInt >= 0)
                    {
                        deviceIdentifier = $"hw:{alsaCardInt}";
                        deviceBus = $"{alsaCardInt}";
                    }
                    else
                    {
                        var alsaCard = device.Properties?.GetString("alsa.card");
                        deviceIdentifier = $"hw:{alsaCard}";
                        deviceBus = $"{alsaCard}";
                        // TAKSTAR TAKSTAR BM-621USB Microphone at usb-0000:00:14.0-2.2.2.1, full speed
                        string pattern = @"usb-[\w:.-]+";
                        var alsaBusCard = device.Properties?.GetString("alsa.long_card_name");
                        if (alsaBusCard != null)
                        {
                            Match match = Regex.Match(alsaBusCard, pattern);
                            if (match.Success)
                            {
                                deviceBus = match.Value;
                            }
                        }
                    }

                    useAlsaSrc = true;
                }
                else
                {
                    deviceIdentifier = $"alsa_input.{udevId}.{profileName}";
                    deviceBus = device.Properties?.GetString("device.bus_path");
                }

                if (string.IsNullOrEmpty(deviceBus))
                {
                    logger.LogError("ToAvrMic(): deviceBus is NULL");
                    return null;
                }
            }
            else
            {
                logger.LogError($"Platform error! {RuntimeInformation.RuntimeIdentifier}");
                throw new NotSupportedException();
            }

            converted = true;
            return new AvrMic
            {
                DisplayName = displayName,
                Identifier = deviceIdentifier,
                Index = deviceIndex,
                Bus = deviceBus,
                UseAlsaSrc = useAlsaSrc
            };
        }
        finally
        {
            logger.LogDebug(converted
                ? $"Device {device.DisplayName} ({device.Name}) has been converted to AvrMic"
                : $"Device {device.DisplayName} ({device.Name}) is invalid AvrMic");
        }
    }
}