using System.Runtime.InteropServices;

namespace AvrStream.Base.Streamer.Devices;

public class AvrCam : AvrDevice
{
    public AvrCam(List<AvrVideoCap> caps, AvrCamFocus focus)
    {
        Caps = caps;
        Focus = focus;
    }

    public List<AvrVideoCap> Caps { get; }
    public AvrCamFocus Focus { get; }

    public string GetGstInput(string? name = null, bool? doTimestamp = null)
    {
        string src;
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            src = $"v4l2src device={Identifier} io-mode=4";
        else throw new NotSupportedException();

        if (!string.IsNullOrEmpty(name)) src = $"{src} name={name}";
        if (doTimestamp.HasValue) src = $"{src} do-timestamp={doTimestamp}";

        return src;
    }
}