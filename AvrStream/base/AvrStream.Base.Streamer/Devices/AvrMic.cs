using System.Runtime.InteropServices;

namespace AvrStream.Base.Streamer.Devices;

public class AvrMic : AvrDevice
{
    public bool UseAlsaSrc { get; set; }

    public string GetGstInput(string? name = null, bool? doTimestamp = null)
    {
        string src;
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            src = $"{(UseAlsaSrc ? "alsasrc" : "pulsesrc")} device={Identifier}";
        else throw new NotSupportedException();

        if (!string.IsNullOrEmpty(name)) src = $"{src} name={name}";
        if (doTimestamp.HasValue) src = $"{src} do-timestamp={doTimestamp}";

        return $"{src}";
    }
}