using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;

namespace AvrStream.Base.Jwt
{
    public class Token
    {
        private readonly IConfiguration _configuration;
        private readonly SymmetricSecurityKey _key;
        private readonly ILogger<Token> _logger;

        public Token(IConfiguration configuration, ILogger<Token> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration.GetJwtKey()));
        }
        public string CreateToken(List<Claim> claims)
        {
            try
            {
                var credentials = new SigningCredentials(_key, SecurityAlgorithms.HmacSha256Signature);
                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(claims),
                    Expires = DateTime.Now.AddDays(_configuration.GetJwtExpiresDay()),
                    SigningCredentials = credentials
                };
                var tokenHandler = new JwtSecurityTokenHandler();
                var jwt = tokenHandler.CreateToken(tokenDescriptor);
                return tokenHandler.WriteToken(jwt);
            }
            catch (Exception e)
            {
                _logger.LogError(e, null);
                throw;
            }
        }
    }
}
