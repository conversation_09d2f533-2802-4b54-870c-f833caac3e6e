using Microsoft.Extensions.Configuration;

namespace AvrStream.Base.Jwt;

public static class JwtExtensions
{
    public static int GetJwtExpiresDay(this IConfiguration configuration)
    {
        return configuration.GetValue<int>("Jwt:ExpiresInDays");
    }

    public static string GetJwtIssuer(this IConfiguration configuration)
    {
        return configuration.GetValue<string>("Jwt:Issuer");
    }

    public static string GetJwtKey(this IConfiguration configuration)
    {
        return configuration.GetValue<string>("Jwt:Key");
    }
}