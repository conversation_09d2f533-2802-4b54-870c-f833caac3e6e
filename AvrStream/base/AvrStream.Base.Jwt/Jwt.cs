using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
namespace AvrStream.Base.Jwt;

public class Jwt
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<Jwt> _logger;
    public Jwt(IConfiguration configuration, ILogger<Jwt> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }
    public ClaimsPrincipal DecodeJwtToken(string token)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration.GetJwtKey() ?? string.Empty));
        var validationParameters = new TokenValidationParameters
        {
            ValidateIssuer = false,
            ValidateAudience = false,
            IssuerSigningKey = key,
            ValidateIssuerSigningKey = true
        };

        try
        {
            var claimsPrincipal = tokenHandler.ValidateToken(token, validationParameters, out _);
            return claimsPrincipal;
        }
        catch (Exception ex)
        {
            // Token validation failed
            _logger.LogError($"Token validation failed: {ex.Message}");
            return null;
        }
    }
}