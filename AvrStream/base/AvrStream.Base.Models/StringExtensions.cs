using System.Text;

namespace AvrStream.Base.Models;

public static class StringExtensions
{
    public static string GenerateRandomString(int length)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        const string specialChars = "~!@#$%^&*";
        const string numbers = "0123456789";
        int index;
        StringBuilder stringBuilder = new StringBuilder();
        Random random = new Random();

        for (int i = 0; i < length - 2; i++)
        {
            index = random.Next(chars.Length);
            stringBuilder.Append(chars[index]);
        }
        index = random.Next(specialChars.Length);
        stringBuilder.Append(specialChars[index]);
        index = random.Next(numbers.Length);
        stringBuilder.Append(numbers[index]);
        return stringBuilder.ToString();
    }
}