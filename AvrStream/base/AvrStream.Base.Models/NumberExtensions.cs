namespace AvrStream.Base.Models;

public static class NumberExtensions
{
    public static double ConvertToGb(char unit, double value)
    {
        switch (unit)
        {
            case 'M':
                return value /= 1024;
            case 'G':
                return value;
            case 'T':
                return value *= 1024;
            default:
                return value;
        }
    }
}