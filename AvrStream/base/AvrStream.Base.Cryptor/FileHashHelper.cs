using System.Security.Cryptography;
using System.Text;

namespace AvrStream.Base.Cryptor;

public class FileHashHelper
{
    public static string CalculateFileSha512Hash(string filePath)
    {
        using SHA512 sha512 = SHA512.Create();
        using FileStream fileStream = File.OpenRead(filePath);
        // Compute the hash for the file
        byte[] hashBytes = sha512.ComputeHash(fileStream);

        // Convert hash bytes to hex string
        StringBuilder hashString = new StringBuilder();
        foreach (byte b in hashBytes)
        {
            hashString.Append(b.ToString("X2"));
        }

        return hashString.ToString();
    }
}