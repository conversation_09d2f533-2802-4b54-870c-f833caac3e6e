using AvrStream.Vms.Api.Controllers;
using AvrStream.Vms.Api.Hub;
using AvrStream.Vms.Api.Services;
using Microsoft.AspNetCore.SignalR;

namespace AvrStream.Vms.Api.Workers;

public class SystemInfoWorker : IHostedService
{
    private readonly IHubContext<DashboardHub, IDashboardHubClient> _hubContext;
    private readonly ResourceService _resourceService;
    private readonly ILogger<SystemInfoWorker> _logger;
    private readonly IConfiguration _configuration;
    public SystemInfoWorker(ILogger<SystemInfoWorker> logger, IConfiguration configuration, 
        ResourceService resourceService, IHubContext<DashboardHub, IDashboardHubClient> hubContext)
    {
        _logger = logger;
        _configuration = configuration;
        _resourceService = resourceService;
        _hubContext = hubContext;
    }
    public Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("SystemInfoWorker is started.");
        Task.Run(async () =>
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                GetResource(cancellationToken);
                await _hubContext.Clients.All.SendDashboardInfo(DashboardController.DashboardInfo);
                await Task.Delay(TimeSpan.FromSeconds(3), cancellationToken);
            }
        }, cancellationToken);
        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("SystemInfoWorker is stopped.");
        return Task.CompletedTask;
    }

    private void GetResource(CancellationToken cancellationToken)
    {
        Task.Run(() =>
        {
            try
            {
                var path = _configuration.GetValue<string>("UploadPath");
                var cpu = Math.Round(_resourceService.GetCpu(), 2);
                var memory = Math.Round(_resourceService.GetMemory(), 2);
                var diskUsed = _resourceService.GetDiskUsed(path);
                var diskTotal = _resourceService.GetDiskTotal(path);
                var cpuTemp = _resourceService.GetCpuTemperature();
                DashboardController.DashboardInfo.Cpu = cpu;
                DashboardController.DashboardInfo.Ram = memory;
                DashboardController.DashboardInfo.DiskUsed = diskUsed;
                DashboardController.DashboardInfo.DiskTotal = diskTotal;
                DashboardController.DashboardInfo.CpuTemperature = cpuTemp;
            }
            catch (Exception e)
            {
                _logger.LogError(e, null);
            }
        }, cancellationToken);
    }
}