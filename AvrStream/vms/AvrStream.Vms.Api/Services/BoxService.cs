using System.Security.Claims;
using System.Text.Json;
using AvrStream.Base.Models;
using AvrStream.Vms.Api.Dto;
using AvrStream.Vms.Api.Extensions;
using AvrStream.Vms.Api.Models;
using AvrStream.Vms.Api.Models.Requests;
using AvrStream.Vms.Api.Models.Responses;
using AvrStream.Vms.Api.Securities;
using AvrStream.Vms.Entities.Databases;
using AvrStream.Vms.Entities.Models;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Vms.Api.Services;

public class BoxService
{
    private readonly VmsDbContext _db;
    private readonly ClientToken _token;
    private readonly IConfiguration _configuration;
    private readonly RecordingFileService _recordingFileService;

    public BoxService(VmsDbContext db, ClientToken token, IConfiguration configuration, RecordingFileService recordingFileService)
    {
        _db = db;
        _token = token;
        _configuration = configuration;
        _recordingFileService = recordingFileService;
    }

    public async Task<ListResult<BoxDto>> ListBoxesAsync(ListBoxRequest request)
    {
        var query = _db.Boxes.AsQueryable();
        var total = await query.CountAsync();
        if (!request.IsTakeAll)
        {
            query = query.Skip((request.Page - 1) * request.Take).Take(request.Take);
        }
        var boxes = await query.Select(b => new BoxDto
        {
            Id = b.Id,
            UrlStream1 = b.UrlStream1,
            UrlStream2 = b.UrlStream2,
            IpAddress = b.IpAddress,
            UniqueId = b.UniqueId,
            Name = b.Name
        }).ToListAsync();
        return new ListResult<BoxDto>(boxes, total);
    }

    public async Task<BaseResponse> DeleteBoxAsync(Guid id)
    {
        var box = await _db.Boxes.SingleOrDefaultAsync(b => b.Id == id);
        if (box == null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Box does not exist"
            };
        }

        var recordingFiles = await _db.RecordingFiles.Where(f => f.BoxId == id).ToListAsync();
        foreach (var recordingFile in recordingFiles)
        {
            var result = await _recordingFileService.DeleteRecordingFileAsync(recordingFile.Id);
            if (!result.Success)
            {
                return new BaseResponse
                {
                    Success = false,
                    Message = $"Deleting box failed because file {recordingFile.Name} could not be deleted"
                };
            }
        }
        _db.Remove(box);
        await _db.SaveChangesAsync();
        return new BaseResponse
        {
            Success = true,
            Message = "Delete box successfully"
        };
    }

    public async Task<AuthenticateBoxResponse> AuthenticateBoxAsync(AuthenticateBoxRequest request)
    {
        var box = await _db.Boxes.SingleOrDefaultAsync(b => b.UniqueId == request.Username);
        if (box == null)
        {
            return new AuthenticateBoxResponse
            {
                Success = false,
                Message = "Box does not exist"
            };
        }

        var passwordHash = SecurityExtensions.CreateMd5(request.Password);
        if (passwordHash != box.Password)
        {
            return new AuthenticateBoxResponse
            {
                Success = false,
                Message = "Passwords do not match"
            };
        }

        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.NameIdentifier, request.Username)
        };
        var issuer = _configuration.GetValue<string>("ClientJwt:Issuer");
        var audience = _configuration.GetValue<string>("ClientJwt:Audience");
        var token = _token.CreateToken(issuer, audience, claims);
        return new AuthenticateBoxResponse
        {
            Token = token,
            Success = true
        };
    }

    public async Task<BaseResponse> CreateBoxAsync(CreateOrUpdateBoxRequest request)
    {
        var existIpAddress = await _db.Boxes.AnyAsync(b => b.IpAddress == request.IpAddress && b.UniqueId != request.UniqueId);
        if (existIpAddress)
        {
            return new BaseResponse
            {
                Success = false,
                Message = $"Box with ip {request.IpAddress} already exists"
            };
        }
        var box = await _db.Boxes.SingleOrDefaultAsync(b => b.UniqueId == request.UniqueId);
        if (box != null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = $"Box with id {request.UniqueId} already exists"
            };
        }
        else
        {
            var newBox = new Box
            {
                IpAddress = request.IpAddress,
                UrlStream1 = request.UrlStream1,
                UrlStream2 = request.UrlStream2,
                Username = request.UniqueId,
                Password = SecurityExtensions.CreateMd5(request.UniqueId),
                Name = request.Name,
                UniqueId = request.UniqueId
            };
            await _db.AddAsync(newBox);
            await _db.SaveChangesAsync();
            return new BaseResponse
            {
                Success = true,
                Message = "New box added successfully"
            };
        }
    }

    public async Task<BaseResponse> UpdateBoxAsync(CreateOrUpdateBoxRequest request)
    {
        var box = await _db.Boxes.SingleOrDefaultAsync(b => b.UniqueId == request.UniqueId);
        if (box == null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Box does not exist"
            };
        }
        box.UrlStream1 = request.UrlStream1;
        box.UrlStream2 = request.UrlStream2;
        box.Name = request.Name;
        _db.Update(box);
        await _db.SaveChangesAsync();
        return new BaseResponse
        {
            Success = true,
            Message = "Box update successful"
        };
    }

    public async Task<BaseResponse> CheckBoxRegisterAsync(string id)
    {
        var box = await _db.Boxes.SingleOrDefaultAsync(b => b.UniqueId == id);
        if (box == null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Box does not register"
            };
        }

        return new BaseResponse
        {
            Success = true,
            Message = "Box already register"
        };
    }

    public async Task<BoxDashboardInfoResponse> GetBoxDashboardInfoAsync(Guid id)
    {
        using var httpClient = new HttpClient();
        var box = await _db.Boxes.SingleOrDefaultAsync(b => b.Id == id);
        if (box == null)
        {
            return new BoxDashboardInfoResponse
            {
                Success = false,
                Message = "Box does not exists"
            };
        }

        var result = await httpClient.GetAsync($"http://{box.IpAddress}/api/Dashboard/GetDashboardInfo");
        var r = await result.Content.ReadAsStringAsync();
        if (result.IsSuccessStatusCode)
        {
            var response = JsonSerializer.Deserialize<BoxDashboardInfo>(r);
            return new BoxDashboardInfoResponse
            {
                Success = true,
                Info = response
            };
        }

        return new BoxDashboardInfoResponse
        {
            Success = false,
            Message = r
        };
    }

    public async Task<BoxResponse> GetBoxAsync(Guid id)
    {
        var box = await _db.Boxes.SingleOrDefaultAsync(b => b.Id == id);
        if (box == null)
        {
            return new BoxResponse
            {
                Success = false,
                Message = "Box does not exist"
            };
        }

        return new BoxResponse
        {
            Box = new BoxDto
            {
                Id = box.Id,
                IpAddress = box.IpAddress,
                Name = box.Name,
                UniqueId = box.UniqueId,
                UrlStream1 = box.UrlStream1,
                UrlStream2 = box.UrlStream2
            },
            Success = true
        };
    }

    public async Task<BaseResponse> ResetBoxAsync(Guid id)
    {
        var box = await _db.Boxes.SingleOrDefaultAsync(b => b.Id == id);
        if (box == null)
        {
            return new BoxResponse
            {
                Success = false,
                Message = "Box does not exist"
            };
        }
        using var httpClient = new HttpClient();
        var result = await httpClient.GetAsync($"http://{box.IpAddress}/api/box/reset-box");
        var r = await result.Content.ReadAsStringAsync();
        if (result.IsSuccessStatusCode)
        {
            var response = JsonSerializer.Deserialize<BaseResponse>(r);
            return response;
        }

        return new BaseResponse
        {
            Success = false,
            Message = r
        };
    }
}