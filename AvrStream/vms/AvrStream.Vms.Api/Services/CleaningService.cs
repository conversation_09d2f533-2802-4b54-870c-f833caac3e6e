using AvrStream.Vms.Api.Models.Requests;
using Timer = System.Timers.Timer;

namespace AvrStream.Vms.Api.Services;

public class CleaningService : IHostedService, IDisposable
{
    private const int Interval = 1000 * 30 * 10;
    private readonly RecordingFileService _recordingFileService;
    private readonly ResourceService _resourceService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<CleaningService> _logger;
    private string _rootPath;
    private double? _percent;
    private int? _numberDateTimeout;
    private Timer _timer;

    public CleaningService(IConfiguration configuration, ILogger<CleaningService> logger, RecordingFileService recordingFileService, ResourceService resourceService)
    {
        _recordingFileService = recordingFileService;
        _resourceService = resourceService;
        _logger = logger;
        _configuration = configuration;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Cleaning Service is started.");
        _rootPath = _configuration["UploadPath"];
        _percent = _configuration.GetValue<double?>("PercentDiskUsed");
        _numberDateTimeout = _configuration.GetValue<int?>("NumberDateTimeout");
        if (_numberDateTimeout == null && _percent == null)
        {
            return Task.CompletedTask;
        }
        _timer = new Timer(Interval);
        _timer.Elapsed += async (sender, args) =>
        {
            try
            {
                _logger.LogInformation($"Root Path: {_rootPath}");
                _logger.LogInformation($"Percent: {_percent}");
                _logger.LogInformation($"NumberDateTimeout: {_numberDateTimeout}");
                if (_numberDateTimeout != null)
                {
                    _logger.LogInformation("Start check Number Date Timeout ...");
                    var lastDate = DateTime.Now.Date.Subtract(TimeSpan.FromDays((double)_numberDateTimeout));
                    var result = await _recordingFileService.ListRecordingFilesAsync(new ListRecordingFileRequest
                    {
                        IsTakeAll = true
                    });
                    var sessionTimeouts = result.Data.Where(x => x.RecordingDate.Date <= lastDate).ToList();
                    foreach (var recordingFile in sessionTimeouts)
                    {
                        try
                        {
                            var deleteResult = await _recordingFileService.DeleteRecordingFileAsync(recordingFile.Id);
                            _logger.LogInformation($"Delete file: {recordingFile.Path}");
                            _logger.LogInformation(deleteResult.Message);
                        }
                        catch (Exception e)
                        {
                            _logger.LogError(e, null);
                        }
                    }
                }

                if (_percent != null)
                {
                    _logger.LogInformation($"Started check capacity ...");
                    var checkResourceExceeded = _resourceService.CheckExceededCapacity((double)_percent, _rootPath);
                    while (checkResourceExceeded)
                    {
                        try
                        {
                            var lastSession = await _recordingFileService.GetRecordingFileAtLastAsync();
                            _logger.LogInformation($"Last Session Created Date: {lastSession.RecordingDate}");
                            var deleteResult = await _recordingFileService.DeleteRecordingFileAsync(lastSession.Id);
                            checkResourceExceeded = _resourceService.CheckExceededCapacity((double)_percent, _rootPath);
                        }
                        catch (Exception e)
                        {
                            _logger.LogError(e, null);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, null);
            }
        };
        _timer.Start();
        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Cleaning Service is stopped.");
        return Task.CompletedTask;
    }
    public void Dispose()
    {
        _timer?.Stop();
        _timer?.Close();
        _timer?.Dispose();
    }
}