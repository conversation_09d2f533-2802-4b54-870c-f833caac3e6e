using AvrStream.Base.Models;
using System.Diagnostics;
using System.Runtime.InteropServices;

namespace AvrStream.Vms.Api.Services;

public class ResourceService
{
    private readonly ILogger<ResourceService> _logger;

    public ResourceService(ILogger<ResourceService> logger)
    {
        _logger = logger;
    }

    public string GetDiskUsed(string disk)
    {
        try
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var info = new ProcessStartInfo("disk used")
                {
                    FileName = "/bin/bash",
                    Arguments = $"-c \"df -h --output=used {disk} | grep -v Used\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                _logger.LogInformation($"Used Command: {info.FileName} {info.Arguments}");
                using var process = Process.Start(info);
                if (process != null && process.WaitForExit(3000))
                {
                    var output = process!.StandardOutput.ReadToEnd();
                    _logger.LogInformation($"Output: {output}");
                    var lines = output.Split("\n");
                    _logger.LogInformation($"Output: {lines[0]}");
                    return lines[0];
                }
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                return "0 G";
            }

            return "0 G";
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return null;
        }
    }

    public string GetDiskTotal(string disk)
    {
        try
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var info = new ProcessStartInfo("disk total")
                {
                    FileName = "/bin/bash",
                    Arguments = $"-c \"df -h --output=size {disk} | grep -v Size\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };
                _logger.LogInformation($"Total Command: {info.FileName} {info.Arguments}");
                using var process = Process.Start(info);
                if (process != null && process.WaitForExit(3000))
                {
                    var output = process!.StandardOutput.ReadToEnd();
                    var lines = output.Split("\n");
                    _logger.LogInformation($"Output: {lines[0]}");
                    return lines[0];
                }
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                return "0 G";
            }

            return "0 G";
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return null;
        }
    }

    public bool CheckExceededCapacity(double percent, string disk)
    {
        var used = GetDiskUsed(disk);
        var total = GetDiskTotal(disk);
        _logger.LogInformation($"Disk: {disk}");
        _logger.LogInformation($"Used Value Raw: {used}");
        _logger.LogInformation($"Total Value Raw: {total}");
        if (!string.IsNullOrEmpty(used) && !string.IsNullOrEmpty(total))
        {
            var usedUnit = used[^1];
            var totalUnit = total[^1];
            var usedValueString = used.Substring(0, used.Length - 1).Replace(",", ".");
            var totalValueString = total.Substring(0, total.Length - 1).Replace(",", ".");
            double.TryParse(usedValueString, out double usedValue);
            double.TryParse(totalValueString, out double totalValue);
            if (usedUnit != totalUnit)
            {
                usedValue = NumberExtensions.ConvertToGb(usedUnit, usedValue);
                totalValue = NumberExtensions.ConvertToGb(totalUnit, totalValue);
            }
            _logger.LogInformation($"Used Value: {usedValue} GB");
            _logger.LogInformation($"Total Value: {totalValue} GB");
            if (totalValue > 0)
            {
                double percentUsed = usedValue / totalValue * 100;
                _logger.LogInformation($"Percent Used: {Math.Round(percentUsed, 3)}");
                if (percentUsed >= percent)
                {
                    return true;
                }
            }
        }
        return false;
    }

    public double GetMemory()
    {
        try
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var info = new ProcessStartInfo("memory")
                {
                    FileName = "/bin/bash",
                    Arguments = "-c \"free -m\"",
                    RedirectStandardOutput = true
                };

                using var process = Process.Start(info);
                if (process != null && process.WaitForExit(3000))
                {
                    var output = process!.StandardOutput.ReadToEnd();

                    var lines = output.Split("\n");
                    var memory = lines[1].Split(" ", StringSplitOptions.RemoveEmptyEntries);
                    return Math.Round(double.Parse(memory[2]) * 100 / double.Parse(memory[1]), 2);
                }
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
        }
        return 0;
    }

    public double GetCpu()
    {
        try
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var info = new ProcessStartInfo("cpu")
                {
                    FileName = "/bin/bash",
                    Arguments =
                        "-c \"top -bn2 | grep '%Cpu' | tail -1 | grep -P '(....|...) id,'|awk '{print 100-$8}'\"",
                    RedirectStandardOutput = true
                };

                using var process = Process.Start(info);
                if (process != null && process.WaitForExit(10000))
                {
                    var output = process!.StandardOutput.ReadToEnd();
                    var lines = output.Split("\n");

                    return Convert.ToDouble(lines[0]);
                }

            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
        }

        return 0;
    }

    public int GetCpuTemperature()
    {
        try
        {
            var info = new ProcessStartInfo("cpu temperature")
            {
                FileName = "/bin/bash",
                Arguments = "-c \"cat /sys/class/thermal/thermal_zone1/temp\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };
            _logger.LogDebug($"Get CPU Temperature Command: {info.FileName} {info.Arguments}");
            using var process = Process.Start(info);
            if (process != null && process.WaitForExit(1000))
            {
                var output = process!.StandardOutput.ReadToEnd();
                var valueString = output.Trim();
                int.TryParse(valueString, out int value);
                return value / 1000;
            }

            return -1;
        }
        catch (Exception e)
        {
            _logger.LogError(e, null);
            return -1;
        }
    }
}