using AvrStream.Base.Cryptor;
using AvrStream.Base.Models;
using AvrStream.Vms.Api.Dto;
using AvrStream.Vms.Api.Models.Requests;
using AvrStream.Vms.Api.Models.Responses;
using AvrStream.Vms.Api.Securities;
using AvrStream.Vms.Entities.Databases;
using AvrStream.Vms.Entities.Models;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Vms.Api.Services;

public class RecordingFileService
{
    private readonly ILogger<RecordingFileService> _logger;
    private readonly IConfiguration _configuration;
    private readonly AesFileDecryption _aesFileDecryption;
    private readonly IServiceScopeFactory _scopeFactory;

    public RecordingFileService(IServiceScopeFactory scopeFactory, ILogger<RecordingFileService> logger, IConfiguration configuration, AesFileDecryption aesFileDecryption)
    {
        _logger = logger;
        _configuration = configuration;
        _aesFileDecryption = aesFileDecryption;
        _scopeFactory = scopeFactory;
    }

    public async Task<ListResult<RecordingFileDto>> ListRecordingFilesAsync(ListRecordingFileRequest request)
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<VmsDbContext>();
        var query = db.RecordingFiles.Where(f => (string.IsNullOrWhiteSpace(request.Name) || f.Name.Contains(request.Name)) && 
                                                  (request.FromDate == null || f.RecordingDate.Date >= request.FromDate.Value.Date) &&
                                                  (request.EndDate == null || f.RecordingDate.Date <= request.EndDate.Value.Date));
        var total = await query.CountAsync();
        if (!request.IsTakeAll)
        {
            query = query.Skip((request.Page - 1) * request.Take).Take(request.Take);
        }

        var boxes = await db.Boxes.ToListAsync();
        var files = await query.ToListAsync();
        var fileDto = new List<RecordingFileDto>();
        var baseUrl = _configuration.GetValue<string>("ApiUrl");
        foreach (var file in files)
        {
            var box = boxes.SingleOrDefault(b => b.Id == file.BoxId);
            fileDto.Add(new RecordingFileDto
            {
                Name = file.Name,
                BoxId = file.BoxId,
                Hash = file.Hash,
                Id = file.Id,
                BoxUniqueId = box?.UniqueId,
                Path = file.Path,
                IpAddress = box?.IpAddress,
                IsEncrypt = file.IsEncrypt,
                Password = file.Password,
                RecordingDate = file.RecordingDate,
                VideoUrl = $"{baseUrl}/file/GetVideo/{file.Id}",
                BoxName = box?.Name
            });
        }
        return new ListResult<RecordingFileDto>(fileDto, total);
    }

    public async Task<RecordingFileResponse> CreateRecordingFileAsync(CreateRecordingFileRequest request)
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<VmsDbContext>();
        var folderPath = _configuration.GetValue<string>("UploadPath");
        var box = await db.Boxes.SingleOrDefaultAsync(b => b.UniqueId == request.UniqueId);
        if (box == null)
        {
            return new RecordingFileResponse
            {
                Success = false,
                Message = "Box does not exists"
            };
        }

        var folderPathDate = $"{folderPath}/{DateTime.Now.Date:ddMMyyyy}";
        var newFile = new RecordingFile
        {
            Name = request.Name,
            BoxId = box.Id,
            Hash = request.Hash,
            IsEncrypt = request.IsEncrypt,
            Password = request.Password,
            Iv = request.Iv,
            Path = $"{folderPathDate}/{request.Name}",
            RecordingDate = request.RecordingDate,
            BoxUniqueId = request.UniqueId,
            DecryptedFilePath = $"{folderPathDate}/decrypted_{request.Name}"
        };
        await db.AddAsync(newFile);
        await db.SaveChangesAsync();
        if (!Directory.Exists(folderPathDate))
        {
            Directory.CreateDirectory(folderPathDate);
        }
        return new RecordingFileResponse
        {
            Success = true,
            Message = $"File {request.Name} created successfully",
            RecordingFileId = newFile.Id
        };
    }

    public async Task<RecordingFile> GetRecordingFileAsync(Guid id)
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<VmsDbContext>();
        var file = await db.RecordingFiles.SingleOrDefaultAsync(f => f.Id == id);
        return file;
    }

    public async Task<RecordingFileInfoResponse> GetRecordingFileInfo(Guid id)
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<VmsDbContext>();
        var baseUrl = _configuration.GetValue<string>("ApiUrl");
        var file = await db.RecordingFiles.SingleAsync(r => r.Id == id);
        var box = await db.Boxes.SingleAsync(b => b.Id == file.BoxId);
        bool decryptedDone = !file.IsEncrypt || (file.IsEncrypt && File.Exists(file.DecryptedFilePath));
        return new RecordingFileInfoResponse
        {
            Name = file.Name,
            BoxId = file.BoxId,
            BoxName = box.Name,
            DecryptedDone = decryptedDone,
            BoxUniqueId = box.UniqueId,
            Hash = file.Hash,
            Id = file.Id,
            IpAddress = box.IpAddress,
            IsEncrypt = file.IsEncrypt,
            Password = file.Password,
            Path = file.Path,
            RecordingDate = file.RecordingDate,
            VideoUrl = $"{baseUrl}/api/File/GetVideo/{file.Id}",
            DecryptedFilePath = file.DecryptedFilePath
        };
    }

    public async Task<BaseResponse> DeleteRecordingFileAsync(Guid id)
    {
        try
        {
            using var scope = _scopeFactory.CreateScope();
            await using var db = scope.ServiceProvider.GetRequiredService<VmsDbContext>();
            var recordingFile = await db.RecordingFiles.SingleOrDefaultAsync(f => f.Id == id);
            if (recordingFile == null)
            {
                return new BaseResponse
                {
                    Success = false,
                    Message = $"Audio file {id} does not exist"
                };
            }

            if (File.Exists(recordingFile.Path))
            {
                var removeResult = RemoveFile(recordingFile.Path);
                if (!removeResult)
                {
                    return new BaseResponse
                    {
                        Success = false,
                        Message = "Delete recording file failed"
                    };
                }
            }

            if (File.Exists(recordingFile.DecryptedFilePath))
            {
                var removeResult = RemoveFile(recordingFile.DecryptedFilePath);
                if (!removeResult)
                {
                    return new BaseResponse
                    {
                        Success = false,
                        Message = "Delete recording file failed"
                    };
                }
            }

            db.Remove(recordingFile);
            await db.SaveChangesAsync();
            return new BaseResponse
            {
                Success = true,
                Message = $"Deleted recording file {id} successfully"
            };
        }
        catch (Exception e)
        {
            _logger.LogError(e, null);
            return new BaseResponse
            {
                Success = false,
                Message = e.Message
            };
        }
    }

    private bool RemoveFile(string path)
    {
        try
        {
            if (File.Exists(path))
            {
                File.Delete(path);
                return true;
            }
            else
            {
                _logger.LogError($"File {path} does not exists");
                return false;
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, null);
            return false;
        }
    }

    public async Task<RecordingFile> GetRecordingFileAtLastAsync()
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<VmsDbContext>();
        var file = await db.RecordingFiles.OrderBy(f => f.RecordingDate).FirstOrDefaultAsync();
        return file;
    }

    public async Task<BaseResponse> DecryptedFileAsync(Guid id)
    {
        using var scope = _scopeFactory.CreateScope();
        await using var db = scope.ServiceProvider.GetRequiredService<VmsDbContext>();
        var file = await db.RecordingFiles.SingleOrDefaultAsync(f => f.Id == id);
        var passwordBytes = file.Password.ConvertHexStringToByteArray();
        var ivBytes = file.Iv.ConvertHexStringToByteArray();
        await _aesFileDecryption.DecryptMp4FileAsync(file.Path, file.DecryptedFilePath, passwordBytes, ivBytes);
        return new BaseResponse
        {
            Success = true,
            Message = "Decoding success"
        };
    }
}