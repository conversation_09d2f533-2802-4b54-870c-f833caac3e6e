<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <ImplicitUsings>enable</ImplicitUsings>
    <TargetFrameworks>net6.0;net8.0;net9.0</TargetFrameworks>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="ViewModels\**" />
    <Content Remove="ViewModels\**" />
    <EmbeddedResource Remove="ViewModels\**" />
    <None Remove="ViewModels\**" />
    <None Remove="shared\tmp\00000000-0000-0000-0000-000000000000_razerkiyoxv4l2_00000.mp4.chunkcomplete" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.35" />
    <PackageReference Include="Serilog.AspNetCore" Version="6.0.1" />
    <PackageReference Include="tusdotnet" Version="2.8.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\base\AvrStream.Base.Cryptor\AvrStream.Base.Cryptor.csproj" />
    <ProjectReference Include="..\..\base\AvrStream.Base.Jwt\AvrStream.Base.Jwt.csproj" />
    <ProjectReference Include="..\..\base\AvrStream.Base.Models\AvrStream.Base.Models.csproj" />
    <ProjectReference Include="..\AvrStream.Vms.Entities\AvrStream.Vms.Entities.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Enums\" />
    <Folder Include="Workers\" />
    <Folder Include="shared\tmp\" />
  </ItemGroup>

</Project>
