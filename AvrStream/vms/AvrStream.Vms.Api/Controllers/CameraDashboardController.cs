using AvrStream.Base.Jwt;
using AvrStream.Vms.Api.Models;
using AvrStream.Vms.Api.Models.Requests;
using AvrStream.Vms.Api.Services;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace AvrStream.Vms.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CameraDashboardController : ControllerBase
    {
        private readonly CameraDashboardService _cameraDashboardService;
        private readonly Jwt _jwt;
        public CameraDashboardController(CameraDashboardService cameraDashboardService, Jwt jwt)
        {
            _cameraDashboardService = cameraDashboardService;
            _jwt = jwt;
        }

        [HttpGet]
        public async Task<IActionResult> ListCameraDashboardInfosAsync(int page = 1, int take = 10, bool isTakeAll = false, string name = null)
        {
            var token = await HttpContext.GetTokenAsync("access_token");
            if (string.IsNullOrWhiteSpace(token))
            {
                return BadRequest("access_token is invalid");
            }
            var claimsPrincipal = _jwt.DecodeJwtToken(token);
            var userId = claimsPrincipal.Claims.First(c => c.Type == ClaimTypes.NameIdentifier).Value;
            var result = await _cameraDashboardService.ListCameraDashboardInfosAsync(new ListCameraDashboardRequest
            {
                IsTakeAll = isTakeAll,
                Name = name,
                Page = page,
                Take = take,
                UserId = userId
            });
            return Ok(result);
        }

        [HttpPost]
        public async Task<IActionResult> CreateOrUpdateCameraDashboardAsync([FromBody] CameraDashboardInfo cameraDashboardInfo)
        {
            var token = await HttpContext.GetTokenAsync("access_token");
            if (string.IsNullOrWhiteSpace(token))
            {
                return BadRequest("access_token is invalid");
            }
            var claimsPrincipal = _jwt.DecodeJwtToken(token);
            var userId = claimsPrincipal.Claims.First(c => c.Type == ClaimTypes.NameIdentifier).Value;
            cameraDashboardInfo.UserId = userId;
            var result = await _cameraDashboardService.CreateOrUpdateCameraDashboardAsync(cameraDashboardInfo);
            return Ok(result);
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteCameraDashboardAsync([FromQuery] Guid id)
        {
            var result = await _cameraDashboardService.DeleteCameraDashboardAsync(id);
            return Ok(result);
        }
    }
}
