using AvrStream.Vms.Entities.Databases;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Vms.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FileController : ControllerBase
    {
        private readonly VmsDbContext _db;
        private readonly ILogger<FileController> _logger;

        public FileController(VmsDbContext db, ILogger<FileController> logger)
        {
            _db = db;
            _logger = logger;
        }

        [HttpGet]
        [Route("[action]/{id}")]
        public async Task<IActionResult> GetVideo([FromRoute] Guid id)
        {
            var file = await _db.RecordingFiles.SingleAsync(x => x.Id == id);
            if (!System.IO.File.Exists(file.Path))
            {
                return NoContent();
            }
            if (!file.IsEncrypt)
            {
                var fileStream = new FileStream(file.Path, FileMode.Open, FileAccess.Read);
                return File(fileStream, "video/mp4", enableRangeProcessing: true);
            }
            else if (file.IsEncrypt && System.IO.File.Exists(file.DecryptedFilePath))
            {
                var fileStream = new FileStream(file.DecryptedFilePath, FileMode.Open, FileAccess.Read);
                return File(fileStream, "video/mp4", enableRangeProcessing: true);
            }
            else
            {
                return NoContent();
            }
        }
    }
}
