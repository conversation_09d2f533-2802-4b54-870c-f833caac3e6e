using AvrStream.Vms.Api.Models;
using AvrStream.Vms.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AvrStream.Vms.Api.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public class DashboardController : ControllerBase
{
    public static DashboardInfo DashboardInfo = new DashboardInfo();
    private readonly ResourceService _resourceService;
    private readonly IConfiguration _configuration;

    public DashboardController(ResourceService resourceService, IConfiguration configuration)
    {
        _resourceService = resourceService;
        _configuration = configuration;
    }
    [HttpGet]
    public IActionResult GetDashboardInfo()
    {
        var path = _configuration.GetValue<string>("UploadPath");
        var dashboardInfo = new DashboardInfo();
        var cpu = _resourceService.GetCpu();
        var ram = _resourceService.GetMemory();
        var diskUsed = _resourceService.GetDiskUsed(path);
        var diskTotal = _resourceService.GetDiskTotal(path);
        var cpuTemperature = _resourceService.GetCpuTemperature();
        dashboardInfo.Cpu = cpu;
        dashboardInfo.Ram = ram;
        dashboardInfo.DiskUsed = diskUsed;
        dashboardInfo.DiskTotal = diskTotal;
        dashboardInfo.CpuTemperature = cpuTemperature;
        return Ok(dashboardInfo);
    }
}