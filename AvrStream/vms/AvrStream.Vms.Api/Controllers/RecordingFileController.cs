using AvrStream.Vms.Api.Models.Requests;
using AvrStream.Vms.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AvrStream.Vms.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class RecordingFileController : ControllerBase
    {
        private readonly RecordingFileService _recordingFileService;

        public RecordingFileController(RecordingFileService recordingFileService)
        {
            _recordingFileService = recordingFileService;
        }

        [HttpGet("list-recording-files")]
        [Authorize]
        [Authorize(Policy = "GetRecordingFile")]
        public async Task<IActionResult> ListRecordingFilesAsync([FromQuery] int page = 1, [FromQuery] int take = 10, [FromQuery] bool isTakeAll = false, 
            [FromQuery] string name = null, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? endDate = null)
        {
            var result = await _recordingFileService.ListRecordingFilesAsync(new ListRecordingFileRequest
            {
                Page = page,
                Take = take,
                IsTakeAll = isTakeAll,
                Name = name,
                FromDate = fromDate,
                EndDate = endDate
            });
            return Ok(result);
        }

        [HttpPost("create-recording-file")]
        [Authorize(AuthenticationSchemes = "ClientAuth")]
        public async Task<IActionResult> CreateRecordingFileAsync(CreateRecordingFileRequest request)
        {
            var result = await _recordingFileService.CreateRecordingFileAsync(request);
            return Ok(result);
        }

        [HttpDelete]
        [Authorize]
        [Authorize(Policy = "DeleteRecordingFile")]
        public async Task<IActionResult> DeleteRecordingFileAsync([FromQuery] Guid id)
        {
            var result = await _recordingFileService.DeleteRecordingFileAsync(id);
            return Ok(result);
        }

        [HttpGet("decrypt-file/{id:guid}")]
        [Authorize]
        [Authorize(Policy = "GetRecordingFile")]
        public async Task<IActionResult> DecryptedFileAsync(Guid id)
        {
            var result = await _recordingFileService.DecryptedFileAsync(id);
            return Ok(result);
        }

        [HttpGet("recording-file-info/{id:guid}")]
        [Authorize]
        [Authorize(Policy = "GetRecordingFile")]
        public async Task<IActionResult> GetRecordingFileInfoAsync(Guid id)
        {
            var result = await _recordingFileService.GetRecordingFileInfo(id);
            return Ok(result);
        }
    }
}
