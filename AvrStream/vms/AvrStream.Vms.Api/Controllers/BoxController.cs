using System.Text.Json;
using AvrStream.Vms.Api.Models.Requests;
using AvrStream.Vms.Api.Models.Responses;
using AvrStream.Vms.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AvrStream.Vms.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BoxController : ControllerBase
    {
        private readonly BoxService _boxService;
        private readonly HttpClient _httpClient;

        public BoxController(BoxService boxService)
        {
            _boxService = boxService;
            _httpClient = new HttpClient();
        }

        [HttpGet("list-boxes")]
        [Authorize(Policy = "GetBox")]
        public async Task<IActionResult> ListBoxesAsync(int page = 1, int take = 10, bool isTakeAll = false)
        {
            var result = await _boxService.ListBoxesAsync(new ListBoxRequest
            {
                IsTakeAll = isTakeAll,
                Page = page,
                Take = take
            });
            return Ok(result);
        }

        [HttpPost("create")]
        public async Task<IActionResult> CreateBoxAsync(CreateOrUpdateBoxRequest request)
        {
            var result = await _boxService.CreateBoxAsync(request);
            return Ok(result);
        }

        [HttpPost("update-client")]
        [Authorize(AuthenticationSchemes = "ClientAuth")]
        public async Task<IActionResult> UpdateBoxByClientAsync(CreateOrUpdateBoxRequest request)
        {
            var result = await _boxService.UpdateBoxAsync(request);
            return Ok(result);
        }

        [HttpDelete("delete")]
        [Authorize(Policy = "DeleteBox")]
        public async Task<IActionResult> DeleteBoxAsync([FromQuery] Guid id)
        {
            var result = await _boxService.DeleteBoxAsync(id);
            return Ok(result);
        }

        [HttpPost("authenticate")]
        [Consumes("application/x-www-form-urlencoded")]
        public async Task<IActionResult> AuthenticateBoxAsync([FromForm] AuthenticateBoxRequest request)
        {
            var result = await _boxService.AuthenticateBoxAsync(request);
            return Ok(result);
        }

        [HttpGet("check-box-exists/{id}")]
        public async Task<IActionResult> CheckBoxRegisterAsync(string id)
        {
            var result = await _boxService.CheckBoxRegisterAsync(id);
            return Ok(result);
        }

        [HttpGet("box-dashboard-info/{id:guid}")]
        [Authorize(Policy = "GetBox")]
        public async Task<IActionResult> GetBoxDashboardInfoAsync(Guid id)
        {
            var result = await _boxService.GetBoxDashboardInfoAsync(id);
            return Ok(result);
        }

        [HttpGet("get-box/{id:guid}")]
        public async Task<IActionResult> GetBoxAsync(Guid id)
        {
            var result = await _boxService.GetBoxAsync(id);
            return Ok(result);
        }

        [HttpGet("start/{id:guid}/{cameraId}")]
        public async Task<IActionResult> StartBoxAsync(Guid id, string cameraId)
        {
            var box = await _boxService.GetBoxAsync(id);
            if (box == null)
            {
                return Ok(new BaseResponse
                {
                    Success = false,
                    Message = "Device not found"
                });
            }
            var result = await _httpClient.GetAsync($"http://{box.Box.IpAddress}/api/box/start/{cameraId}");
            var r = await result.Content.ReadAsStringAsync();
            if (result.IsSuccessStatusCode)
            {
                var response = JsonSerializer.Deserialize<BaseResponse>(r);
                return Ok(response);
            }
            else
            {
                return Ok(new BaseResponse
                {
                    Success = false,
                    Message = r
                });
            }
        }

        [HttpGet("stop/{id:guid}/{cameraId}")]
        public async Task<IActionResult> StopBoxAsync(Guid id, string cameraId)
        {
            var box = await _boxService.GetBoxAsync(id);
            if (box == null)
            {
                return Ok(new BaseResponse
                {
                    Success = false,
                    Message = "Device not found"
                });
            }
            var result = await _httpClient.GetAsync($"http://{box.Box.IpAddress}/api/box/stop/{cameraId}");
            var r = await result.Content.ReadAsStringAsync();
            if (result.IsSuccessStatusCode)
            {
                var response = JsonSerializer.Deserialize<BaseResponse>(r);
                return Ok(response);
            }
            else
            {
                return Ok(new BaseResponse
                {
                    Success = false,
                    Message = r
                });
            }
        }
        
        [HttpGet("restart/{id:guid}/{cameraId}")]
        public async Task<IActionResult> RestartBoxAsync(Guid id, string cameraId)
        {
            var box = await _boxService.GetBoxAsync(id);
            if (box == null)
            {
                return Ok(new BaseResponse
                {
                    Success = false,
                    Message = "Device not found"
                });
            }
            var result = await _httpClient.GetAsync($"http://{box.Box.IpAddress}api/box/stop/{cameraId}");
            var r = await result.Content.ReadAsStringAsync();
            if (result.IsSuccessStatusCode)
            {
                var response = JsonSerializer.Deserialize<BaseResponse>(r);
                return Ok(response);
            }
            else
            {
                return Ok(new BaseResponse
                {
                    Success = false,
                    Message = r
                });
            }
        }

        [HttpGet("reset-box/{id}")]
        public async Task<IActionResult> ResetBox(Guid id)
        {
            var result = await _boxService.ResetBoxAsync(id);
            return Ok(result);
        }
    }
}