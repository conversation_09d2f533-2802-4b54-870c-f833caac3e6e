using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using AvrStream.Base.Jwt;
using AvrStream.Vms.Api.Services;
using AvrStream.Vms.Api.Models.Requests;

namespace AvrStream.Vms.Api.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public class AccountController : ControllerBase
{
    private readonly Jwt _jwt;
    private readonly AccountService _accountService;

    public AccountController(Jwt jwt, AccountService accountService)
    {
        _jwt = jwt;
        _accountService = accountService;
    }

    [HttpGet("list-accounts")]
    [Authorize(Policy = "GetAccount")]
    public async Task<IActionResult> ListAccountsAsync([FromQuery] int page = 1,
            [FromQuery] int take = 10,
            [FromQuery] bool isTakeAll = false,
            [FromQuery] string username = null,
            [FromQuery] string name = null)
    {
        if (page < 1)
        {
            page = 1;
        }

        var result = await _accountService.ListAccountsAsync(new ListAccountRequest
        {
            Page = page,
            Take = take,
            Name = name,
            IsTakeAll = isTakeAll,
            Username = username
        });

        return Ok(result);
    }

    [HttpPost("create-account")]
    [Authorize(Policy = "CreateAccount")]
    public async Task<IActionResult> CreateAccountAsync(CreateAccountRequest request)
    {
        var result = await _accountService.CreateAccountAsync(request);
        return Ok(result);
    }

    [HttpPut("update-account")]
    [Authorize(Policy = "UpdateAccount")]
    public async Task<IActionResult> UpdateAccountAsync(UpdateAccountRequest request)
    {
        var result = await _accountService.UpdateAccountAsync(request);
        return Ok(result);
    }

    [HttpDelete("delete-account")]
    [Authorize(Policy = "DeleteAccount")]
    public async Task<IActionResult> DeleteAccountAsync([FromQuery] string userId)
    {
        var currentUserName = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;
        var result = await _accountService.DeleteAccountAsync(new DeleteAccountRequest
        {
            CurrentUser = currentUserName,
            UserId = userId
        });
        return Ok(result);
    }

    [HttpPost("reset-password")]
    [Authorize(Policy = "ResetPassword")]
    public async Task<IActionResult> ResetPasswordAsync(ResetPasswordRequest request)
    {
        var result = await _accountService.ResetPasswordAsync(request);
        return Ok(result);
    }

    [HttpPost("change-password")]
    public async Task<IActionResult> ChangePasswordAsync(ChangePasswordRequest request)
    {
        var token = await HttpContext.GetTokenAsync("access_token");
        if (string.IsNullOrWhiteSpace(token))
        {
            return BadRequest("access_token is invalid");
        }
        var claimsPrincipal = _jwt.DecodeJwtToken(token);
        var userId = claimsPrincipal.Claims.First(c => c.Type == ClaimTypes.NameIdentifier).Value;
        request.UserId = userId;
        var result = await _accountService.ChangePasswordAsync(request);
        return Ok(result);
    }

    [HttpPost("update-permission")]
    [Authorize(Policy = "UpdatePermission")]
    public async Task<IActionResult> UpdatePermissionAsync(UpdatePermissionRequest request)
    {
        var result = await _accountService.UpdatePermissionAsync(request);
        return Ok(result);
    }
}
