using tusdotnet.Interfaces;

namespace AvrStream.Vms.Api.Tus;

public class AvrFileLock : ITusFileLock
{
    private bool _hasLock;
    private readonly string _fileId;
    private static readonly Dictionary<string, DateTime> LockedFiles = new Dictionary<string, DateTime>();

    /// <summary>
    /// 
    /// </summary>
    /// <param name="fileId"></param>
    public AvrFileLock(string fileId)
    {
        _hasLock = false;
        _fileId = fileId;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public Task<bool> Lock()
    {
        if (_hasLock)
        {
            return Task.FromResult(true);
        }

        lock (LockedFiles)
        {
            if (!LockedFiles.ContainsKey(_fileId))
            {
                LockedFiles.Add(_fileId, DateTime.Now);
                _hasLock = true;
            }
            else
            {
                var createdDate = LockedFiles[_fileId];
                if (createdDate.AddMinutes(5) < DateTime.Now)
                {
                    LockedFiles.Remove(_fileId);
                }
                _hasLock = false;
            }
        }

        return Task.FromResult(_hasLock);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public Task ReleaseIfHeld()
    {
        if (!_hasLock)
        {
            return Task.CompletedTask;
        }

        lock (LockedFiles)
        {
            LockedFiles.Remove(_fileId);
        }

        return Task.CompletedTask;
    }
}