using System.Text;
using tusdotnet.Interfaces;

namespace AvrStream.Vms.Api.Tus;

public class SessionFileIdProvider : ITusFileIdProvider
{
    public Task<string> CreateId(string metadata)
    {
        return Task.FromResult(ConstructFileId(metadata));
    }

    public Task<bool> ValidateId(string fileId)
    {
        return Task.FromResult(true);
    }

    private static string ConstructFileId(string metadata)
    {
        // metadata SHOULD contains sessionId: <id>, fileName: <name>
        // just return <id>_<name>
        var dic = metadata.Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries)
            .Select(t =>
            {
                var idx = t.IndexOf(' ');
                var key = t.Substring(0, idx);
                var val = t.Substring(idx + 1);
                return new { key, val };
            })
            .ToDictionary(t => t.key, t => Encoding.UTF8.GetString(Convert.FromBase64String(t.val)));

        return $"{dic["recordingFileId"]}_{dic["fileName"]}";
    }

    public (Guid recordingFileId, string fileName) DestructFileId(string name)
    {
        var idx = name.IndexOf('_');
        var recordingFileIdString = name.Substring(0, idx);
        Guid.TryParse(recordingFileIdString, out var recordingFileId);
        var fileName = name.Substring(idx + 1);
        return (recordingFileId, fileName);
    }
}