using AvrStream.Vms.Entities.Databases;
using AvrStream.Vms.Entities.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Vms.Api.SeedData;

public class UserDbContextSeed
{
    public static async Task SeedAsync(UserDbContext db, RoleManager<IdentityRole> roleManager, UserManager<AppUser> userManager, ILogger<UserDbContextSeed> logger)
    {
        await SeedRoleAsync(db, roleManager, logger);
        await SeedUserAsync(db, userManager, logger);
    }

    public static async Task SeedRoleAsync(UserDbContext db, RoleManager<IdentityRole> roleManager, ILogger<UserDbContextSeed> logger)
    {
        if (!(await db.Roles.AnyAsync()))
        {
            var roles = new List<RoleSeed>
            {
                new RoleSeed
                {
                    Role = new IdentityRole
                    {
                        Name = "Toàn quyền hệ thống"
                    },
                    RoleClaims = new List<IdentityRoleClaim<string>>
                    {
                        //Account
                        new IdentityRoleClaim<string>
                        {
                            ClaimType = "CreateAccount",
                            ClaimValue = "True"
                        },
                        new IdentityRoleClaim<string>
                        {
                            ClaimType = "UpdateAccount",
                            ClaimValue = "True"
                        },
                        new IdentityRoleClaim<string>
                        {
                            ClaimType = "DeleteAccount",
                            ClaimValue = "True"
                        },
                        new IdentityRoleClaim<string>
                        {
                            ClaimType = "GetAccount",
                            ClaimValue = "True"
                        },
                        new IdentityRoleClaim<string>
                        {
                            ClaimType = "ResetPassword",
                            ClaimValue = "True"
                        },
                        new IdentityRoleClaim<string>
                        {
                            ClaimType = "UpdatePermission",
                            ClaimValue = "True"
                        },
                        //Role
                        new IdentityRoleClaim<string>
                        {
                            ClaimType = "CreateRole",
                            ClaimValue = "True"
                        },
                        new IdentityRoleClaim<string>
                        {
                            ClaimType = "UpdateRole",
                            ClaimValue = "True"
                        },
                        new IdentityRoleClaim<string>
                        {
                            ClaimType = "DeleteRole",
                            ClaimValue = "True"
                        },
                        new IdentityRoleClaim<string>
                        {
                            ClaimType = "GetRole",
                            ClaimValue = "True"
                        },

                        //Box
                        new IdentityRoleClaim<string>
                        {
                            ClaimType = "GetBox",
                            ClaimValue = "True"
                        },
                        new IdentityRoleClaim<string>
                        {
                            ClaimType = "UpdateBox",
                            ClaimValue = "True"
                        },
                        new IdentityRoleClaim<string>
                        {
                            ClaimType = "DeleteBox",
                            ClaimValue = "True"
                        },

                        //View Camera
                        new IdentityRoleClaim<string>
                        {
                            ClaimType = "ViewCamera",
                            ClaimValue = "True"
                        },

                        //Recording File
                        new IdentityRoleClaim<string>
                        {
                            ClaimType = "GetRecordingFile",
                            ClaimValue = "True"
                        },
                        new IdentityRoleClaim<string>
                        {
                            ClaimType = "DeleteRecordingFile",
                            ClaimValue = "True"
                        }
                    }
                }
            };
            foreach (var role in roles)
            {
                await roleManager.CreateAsync(role.Role);
                foreach (var claim in role.RoleClaims)
                {
                    claim.RoleId = role.Role.Id;
                    await db.RoleClaims.AddAsync(claim);
                }
            }
            await db.SaveChangesAsync();
        }
    }

    public static async Task SeedUserAsync(UserDbContext db, UserManager<AppUser> userManager, ILogger<UserDbContextSeed> logger)
    {
        if (!(await db.Users.AnyAsync()))
        {
            var role = await db.Roles.SingleOrDefaultAsync(r => r.Name == "Toàn quyền hệ thống");
            var user = new AppUser
            {
                Email = "<EMAIL>",
                UserName = "admin",
                Id = "7fe58af6-261c-492b-850a-3618e4771b3d",
                Name = "admin"
            };
            await userManager.CreateAsync(user, "admin");
            if (role != null)
            {
                await db.UserRoles.AddAsync(new IdentityUserRole<string>
                {
                    UserId = user.Id,
                    RoleId = role.Id
                });
                await db.SaveChangesAsync();
            }
        }
    }
}