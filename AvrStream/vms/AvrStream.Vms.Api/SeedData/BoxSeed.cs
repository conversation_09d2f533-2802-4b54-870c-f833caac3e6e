using System.Text;
using AvrStream.Vms.Api.Extensions;
using AvrStream.Vms.Entities.Databases;
using AvrStream.Vms.Entities.Models;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Vms.Api.SeedData;

public class BoxSeed
{
    public static string GenerateUniqueId()
    {
        var random = new Random();
        var builder = new StringBuilder();
        for (int i = 0; i < 9; i++)
        {
            builder.Append(random.Next(0, 10));
        }
        return builder.ToString();
    }

    public static List<string> GenerateListUniqueId()
    {
        var list = new List<string>();
        while (list.Count < 200)
        {
            var uniqueId = GenerateUniqueId();
            if (!list.Contains(uniqueId))
            {
                list.Add(uniqueId);
            }
        }
        return list;
    }

    public static async Task GenerateBoxAsync(VmsDbContext db)
    {
        if (!(await db.Boxes.AnyAsync()))
        {
            var listUniqueIds = GenerateListUniqueId();
            var boxes = new List<Box>();
            foreach (var id in listUniqueIds)
            {
                boxes.Add(new Box
                {
                    IpAddress = "***********",
                    UrlStream1 = "/stream1",
                    UrlStream2 = "/stream2",
                    Username = id,
                    Password = SecurityExtensions.CreateMd5(id),
                    Name = $"Box {id}",
                    UniqueId = id
                });
            }

            await db.AddRangeAsync(boxes);
            await db.SaveChangesAsync();
        }
    }
}