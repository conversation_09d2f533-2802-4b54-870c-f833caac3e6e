using AvrStream.Base.Jwt;
using AvrStream.Vms.Api.Extensions;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace AvrStream.Vms.Api.Securities;

public class ClientToken
{
    private readonly SymmetricSecurityKey _key;
    private readonly ILogger<Token> _logger;

    public ClientToken(IConfiguration configuration, ILogger<Token> logger)
    {
        _logger = logger;
        _key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration.GetJwtClientKey()));
    }
    public string CreateToken(string issuer, string audience, List<Claim> claims)
    {
        try
        {
            var credentials = new SigningCredentials(_key, SecurityAlgorithms.HmacSha256Signature);
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                SigningCredentials = credentials,
                Issuer = issuer,
                Audience = audience
            };
            var tokenHandler = new JwtSecurityTokenHandler();
            var jwt = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(jwt);
        }
        catch (Exception e)
        {
            _logger.LogError(e, null);
            throw;
        }
    }
}