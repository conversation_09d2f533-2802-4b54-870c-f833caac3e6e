using System.Security.Cryptography;

namespace AvrStream.Vms.Api.Securities;

public class AesFileDecryption
{
    public async Task DecryptMp4FileAsync(string encryptedFilePath, string outputFilePath, byte[] key, byte[] iv)
    {
        if (key.Length != 32) // 256-bit key
            throw new ArgumentException("Key size must be 256 bits (32 bytes).");
        if (iv.Length != 16) // AES block size for CBC mode
            throw new ArgumentException("IV size must be 16 bytes.");

        await using FileStream encryptedFileStream = new FileStream(encryptedFilePath, FileMode.Open, FileAccess.Read);
        await using FileStream outputFileStream = new FileStream(outputFilePath, FileMode.Create, FileAccess.Write);
        using Aes aes = Aes.Create();
        aes.Key = key;
        aes.IV = iv;
        aes.Mode = CipherMode.CBC;
        aes.Padding = PaddingMode.PKCS7;

        // Create a decryptor to perform the stream transform.
        await using CryptoStream cryptoStream = new CryptoStream(encryptedFileStream, aes.CreateDecryptor(), CryptoStreamMode.Read);
        // Copy data from the crypto stream to the output file stream to decrypt it.
        await cryptoStream.CopyToAsync(outputFileStream);
    }
}