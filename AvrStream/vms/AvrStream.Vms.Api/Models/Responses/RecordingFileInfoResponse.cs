namespace AvrStream.Vms.Api.Models.Responses;

public class RecordingFileInfoResponse
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Password { get; set; }
    public bool IsEncrypt { get; set; }
    public string Path { get; set; }
    public string DecryptedFilePath { get; set; }
    public string Hash { get; set; }
    public DateTime RecordingDate { get; set; }
    public Guid BoxId { get; set; }
    public string BoxName { get; set; }
    public string BoxUniqueId { get; set; }
    public string IpAddress { get; set; }
    public bool DecryptedDone { get; set; }
    public string VideoUrl { get; set; }
}