using System.Text.Json.Serialization;

namespace AvrStream.Vms.Api.Models;

public class BoxDashboardInfo
{
    [JsonPropertyName("cpu")]
    public double Cpu { get; set; }
    [JsonPropertyName("memory")]
    public double Memory { get; set; }
    [Json<PERSON>ropertyName("diskUsed")]
    public string DiskUsed { get; set; }
    [JsonPropertyName("diskTotal")]
    public string DiskTotal { get; set; }
    [JsonPropertyName("cpuTemp")]
    public int CpuTemp { get; set; }
    [JsonPropertyName("statusCam1")]
    public DeviceStatus StatusCam1 { get; set; }
    [JsonPropertyName("statusCam2")]
    public DeviceStatus StatusCam2 { get; set; }
    [JsonPropertyName("statusMic1")]
    public DeviceStatus StatusMic1 { get; set; }
    [JsonPropertyName("statusMic2")]
    public DeviceStatus StatusMic2 { get; set; }
    [JsonPropertyName("messages")]
    public List<MessageBox> Messages { get; set; }
}