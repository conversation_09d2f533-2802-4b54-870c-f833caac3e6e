namespace AvrStream.Vms.Api.Models;

public class Permission
{
    public bool CreateAccount { get; set; }
    public bool UpdateAccount { get; set; }
    public bool DeleteAccount { get; set; }
    public bool GetAccount { get; set; }
    public bool ResetPassword { get; set; }
    public bool UpdatePermission { get; set; }
    public bool CreateRole { get; set; }
    public bool UpdateRole { get; set; }
    public bool DeleteRole { get; set; }
    public bool GetRole { get; set; }
    public bool GetBox { get; set; }
    public bool UpdateBox { get; set; }
    public bool DeleteBox { get; set; }
    public bool GetRecordingFile { get; set; }
    public bool DeleteRecordingFile { get; set; }
    public bool ViewCamera { get; set; }
}