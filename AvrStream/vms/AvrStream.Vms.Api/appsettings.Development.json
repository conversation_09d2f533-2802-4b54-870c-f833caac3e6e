{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Jwt": {"Key": "********************************", "ExpiresInDays": 5, "ExpiresInHours": 4}, "ClientJwt": {"Key": "********************************", "Issuer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Audience": "ClientAudience"}, "ConnectionStrings": {"VmsUser": "Server=localhost;Port=5432;Database=VmsUser_dev;Username=postgres;Password=yourStrong(!!)Pa$$word;", "Vms": "Server=localhost;Port=5432;Database=Vms_dev;Username=postgres;Password=yourStrong(!!)Pa$$word;"}, "ApiUrl": "http://0.0.0.0:5046", "Serilog": {"Using": ["Serilog.Sinks.File", "Serilog.Sinks.Console"], "MinimumLevel": "Information", "WriteTo": [{"Name": "File", "Args": {"path": "%APP_BASE_DIRECTORY%/Logs/VmsApi-log-.txt", "fileSizeLimitBytes": "20971520", "rollOnFileSizeLimit": true, "rollingInterval": "Day", "retainedFileCountLimit": 30}}, {"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}}