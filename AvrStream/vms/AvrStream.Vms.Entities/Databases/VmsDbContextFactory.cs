using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace AvrStream.Vms.Entities.Databases;

public class VmsDbContextFactory : IDesignTimeDbContextFactory<VmsDbContext>
{
    public VmsDbContext CreateDbContext(string[] args)
    {
        var builder = new DbContextOptionsBuilder<VmsDbContext>();
        builder.UseNpgsql("Server=localhost;Port=5432;Database=Vms_dev;Username=postgres;Password=yourStrong(!!)Pa$$word;");
        return new VmsDbContext(builder.Options);
    }
}