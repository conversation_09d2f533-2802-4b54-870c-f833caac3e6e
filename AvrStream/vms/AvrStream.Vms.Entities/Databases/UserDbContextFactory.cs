using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace AvrStream.Vms.Entities.Databases;

public class UserDbContextFactory : IDesignTimeDbContextFactory<UserDbContext>
{
    public UserDbContext CreateDbContext(string[] args)
    {
        var builder = new DbContextOptionsBuilder<UserDbContext>();
        builder.UseNpgsql("Server=localhost;Port=5432;Database=VmsUser_dev;Username=postgres;Password=yourStrong(!!)Pa$$word;");
        return new UserDbContext(builder.Options);
    }
}