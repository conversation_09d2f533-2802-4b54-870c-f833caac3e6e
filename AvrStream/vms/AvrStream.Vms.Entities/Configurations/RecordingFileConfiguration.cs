using AvrStream.Vms.Entities.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AvrStream.Vms.Entities.Configurations;

public class RecordingFileConfiguration : IEntityTypeConfiguration<RecordingFile>
{
    public void Configure(EntityTypeBuilder<RecordingFile> builder)
    {
        builder.ToTable("RecordingFiles");
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).ValueGeneratedOnAdd();
        builder.Property(x => x.Name).HasMaxLength(500);
        builder.Property(x => x.Path).HasMaxLength(5000);
        builder.Property(x => x.Password).HasMaxLength(500);
        builder.Property(x => x.Hash).HasMaxLength(5000);
        builder.Property(x => x.BoxUniqueId).HasMaxLength(500);
        builder.Property(x => x.Iv).HasMaxLength(500);
        builder.Property(x => x.DecryptedFilePath).HasMaxLength(5000);
    }
}