using AvrStream.Vms.Entities.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AvrStream.Vms.Entities.Configurations;

public class CameraViewSettingConfiguration : IEntityTypeConfiguration<CameraViewSetting>
{
    public void Configure(EntityTypeBuilder<CameraViewSetting> builder)
    {
        builder.ToTable("CameraViewSettings");
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).ValueGeneratedOnAdd();
        builder.Property(x => x.UniqueId).HasMaxLength(500);
        builder.Property(x => x.UrlStream).HasMaxLength(500);
    }
}