using AvrStream.Vms.Entities.Databases;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace AvrStream.Vms.Entities;

public static class EntitiesExtensions
{
    public static IServiceCollection AddVmsEntities(this IServiceCollection serviceCollection, string cnn)
    {
        serviceCollection
            .AddDbContext<VmsDbContext>(b =>
                b.UseNpgsql(cnn, opts => opts.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery)))
            ;

        return serviceCollection;
    }

    public static IServiceCollection AddUserEntities(this IServiceCollection serviceCollection, string cnn)
    {
        serviceCollection
            .AddDbContext<UserDbContext>(b =>
                b.UseNpgsql(cnn, opts => opts.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery)))
            ;

        return serviceCollection;
    }
}