namespace AvrStream.Vms.Entities.Models;

public class RecordingFile : BaseEntity
{
    public string Name { get; set; }
    public string Password { get; set; }
    public string Iv { get; set; }
    public bool IsEncrypt { get; set; }
    public string Path { get; set; }
    public string DecryptedFilePath { get; set; }
    public string Hash { get; set; }
    public DateTime RecordingDate { get; set; }
    public Guid BoxId { get; set; }
    public string BoxUniqueId { get; set; }
}